# دليل إعداد قاعدة البيانات - مقهى آفي

## نظرة عامة

تم إنشاء قاعدة بيانات محسّنة ومتطورة لمشروع مقهى آفي تتضمن جميع الميزات المطلوبة لإدارة المقهى بكفاءة عالية.

## الجداول المُنشأة

### 1. جدول المستخدمين (users)
- **الغرض**: إدارة المستخدمين والموظفين
- **الأدوار**: admin, manager, barista, waiter, cashier
- **المميزات**: تشفير كلمات المرور، تتبع آخر تسجيل دخول

### 2. جدول الفئات (categories)
- **الغرض**: تصنيف المنتجات
- **المميزات**: دعم متعدد اللغات، ألوان مخصصة، ترتيب العرض
- **الفئات**: مشروبات ساخنة، مشروبات باردة، حلويات، معجنات، وجبات خفيفة

### 3. جدول المنتجات (products)
- **الغرض**: إدارة قائمة الطعام والمشروبات
- **المميزات**: 
  - أسعار التكلفة والبيع
  - رموز SKU فريدة
  - وقت التحضير
  - السعرات الحرارية
  - العلامات والحساسية
  - دعم متعدد اللغات

### 4. جدول النادلين (waiters)
- **الغرض**: إدارة النادلين والموظفين
- **المميزات**: 
  - ربط بجدول المستخدمين
  - نظام الورديات
  - معدل الأجر والعمولة
  - تتبع الحالة الحالية
  - إحصائيات يومية

### 5. جدول الطاولات (tables)
- **الغرض**: إدارة طاولات المطعم
- **المميزات**: 
  - أرقام وأسماء الطاولات
  - السعة والموقع
  - حالة الطاولة
  - رموز QR للقائمة الرقمية

### 6. جدول الطلبات (orders)
- **الغرض**: إدارة طلبات العملاء
- **المميزات**:
  - أرقام طلبات فريدة
  - حالات متعددة للطلب
  - حالة الدفع
  - حساب الضرائب والخصومات
  - أنواع الطلبات (داخلي، خارجي، توصيل)
  - تتبع وقت التحضير

### 7. جدول عناصر الطلبات (order_items)
- **الغرض**: تفاصيل المنتجات في كل طلب
- **المميزات**: 
  - ربط بالطلبات والمنتجات
  - تعليمات خاصة
  - حالة كل عنصر
  - حفظ اسم المنتج للتاريخ

### 8. جدول المدفوعات (payments)
- **الغرض**: تتبع المدفوعات
- **المميزات**: 
  - طرق دفع متعددة
  - معرفات المعاملات
  - حالة الدفع
  - تتبع المعالج

### 9. جدول المبيعات (sales)
- **الغرض**: التقارير والتحليلات
- **المميزات**: 
  - تتبع العمولات
  - إحصائيات المبيعات
  - ربط بالنادلين والكاشيرين

### 10. جدول المخزون (inventory)
- **الغرض**: إدارة المخزون
- **المميزات**: 
  - تتبع الكميات الحالية
  - حدود الحد الأدنى والأقصى
  - وحدات القياس
  - تاريخ آخر تجديد

### 11. جدول تاريخ حالات الطلبات (order_status_history)
- **الغرض**: تتبع تغييرات حالة الطلبات
- **المميزات**: تسجيل كامل لجميع التغييرات

## المميزات المتقدمة

### 🔒 Row Level Security (RLS)
- حماية البيانات على مستوى الصفوف
- صلاحيات مختلفة للمستخدمين
- وصول عام محدود للقوائم

### ⚡ Triggers والـ Functions
- **update_updated_at_column()**: تحديث تلقائي لـ updated_at
- **generate_order_number()**: توليد أرقام طلبات فريدة
- **calculate_order_totals()**: حساب إجماليات الطلبات
- **track_order_status_change()**: تتبع تغييرات الحالة

### 📊 Indexes للأداء
- فهارس محسّنة لجميع الاستعلامات الشائعة
- تحسين أداء البحث والفلترة

### 🌐 دعم متعدد اللغات
- أسماء ووصف بالعربية والإنجليزية
- مرونة في العرض

## خطوات التنفيذ

### 1. تشغيل ملف إنشاء الجداول
```sql
-- في SQL Editor في Supabase
-- انسخ والصق محتوى ملف supabase-schema.sql
```

### 2. تشغيل ملف البيانات النموذجية
```sql
-- في SQL Editor في Supabase
-- انسخ والصق محتوى ملف supabase-seed-data.sql
```

### 3. التحقق من النتائج
```sql
-- فحص الجداول
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public';

-- فحص البيانات
SELECT COUNT(*) FROM categories;
SELECT COUNT(*) FROM products;
SELECT COUNT(*) FROM users;
```

## البيانات النموذجية المُدرجة

### المستخدمين
- مدير النظام (<EMAIL>)
- مدير المقهى (<EMAIL>)
- باريستا (<EMAIL>)

### النادلين
- 4 نادلين بورديات مختلفة
- معدلات أجور وعمولات

### الطاولات
- 8 طاولات بسعات مختلفة
- مواقع متنوعة (داخلي، خارجي، VIP)

### المنتجات
- **مشروبات ساخنة**: 6 منتجات
- **مشروبات باردة**: 6 منتجات  
- **حلويات**: 5 منتجات
- **معجنات**: 4 منتجات
- **وجبات خفيفة**: 4 منتجات

### المخزون
- بيانات مخزون لجميع المنتجات
- حدود دنيا وعليا مناسبة

### طلب نموذجي
- طلب مكتمل مع عناصر ومدفوعات
- سجل مبيعات مرتبط

## الاستعلامات المفيدة

### عرض المنتجات الشائعة
```sql
SELECT name, name_en, price, is_popular 
FROM products 
WHERE is_popular = true 
ORDER BY price DESC;
```

### تقرير المبيعات اليومية
```sql
SELECT 
    waiter_name,
    COUNT(*) as orders_count,
    SUM(net_amount) as total_sales,
    SUM(commission_amount) as total_commission
FROM sales 
WHERE sale_date = CURRENT_DATE
GROUP BY waiter_name;
```

### حالة المخزون المنخفض
```sql
SELECT 
    p.name,
    i.current_stock,
    i.minimum_stock
FROM inventory i
JOIN products p ON i.product_id = p.id
WHERE i.current_stock <= i.minimum_stock;
```

### الطلبات النشطة
```sql
SELECT 
    o.order_number,
    o.table_number,
    o.status,
    o.assigned_waiter_name,
    o.total_amount
FROM orders o
WHERE o.status IN ('pending', 'confirmed', 'preparing', 'ready')
ORDER BY o.created_at;
```

## ملاحظات مهمة

1. **كلمات المرور**: يجب تشفيرها في التطبيق الفعلي
2. **الصور**: تحديث مسارات الصور حسب التطبيق
3. **العملة**: جميع الأسعار بالريال السعودي
4. **التوقيت**: استخدام UTC مع تحويل محلي
5. **النسخ الاحتياطي**: إنشاء نسخ احتياطية دورية

## الدعم والصيانة

- مراقبة أداء الاستعلامات
- تحديث الفهارس حسب الحاجة
- مراجعة صلاحيات RLS دورياً
- تنظيف البيانات القديمة

---

**تم إنشاء قاعدة البيانات بنجاح! 🎉**

جميع الجداول والبيانات جاهزة للاستخدام في مشروع مقهى آفي.
