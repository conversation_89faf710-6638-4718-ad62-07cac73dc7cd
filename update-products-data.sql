-- Update Products Data Script
-- تحديث بيانات المنتجات الموجودة بالمعلومات الإضافية

-- ========================================
-- UPDATE EXISTING PRODUCTS WITH ADDITIONAL DATA
-- ========================================

-- تحديث المشروبات الساخنة
UPDATE products SET 
    cost_price = 8.00,
    sku = 'HD001',
    preparation_time = 8,
    calories = 50,
    tags = ARRAY['قهوة', 'تقليدي', 'هيل']
WHERE name = 'قهوة تركية' AND sku IS NULL;

UPDATE products SET 
    cost_price = 6.00,
    sku = 'HD002',
    preparation_time = 3,
    calories = 30,
    tags = ARRAY['قهوة', 'مركز', 'إيطالي']
WHERE name = 'إسبريسو' AND sku IS NULL;

UPDATE products SET 
    cost_price = 9.00,
    sku = 'HD003',
    preparation_time = 5,
    calories = 120,
    tags = ARRAY['قهوة', 'حليب', 'رغوة']
WHERE name = 'كابتشينو' AND sku IS NULL;

UPDATE products SET 
    cost_price = 9.50,
    sku = 'HD004',
    preparation_time = 5,
    calories = 150,
    tags = ARRAY['قهوة', 'حليب', 'كريمي']
WHERE name = 'لاتيه' AND sku IS NULL;

UPDATE products SET 
    cost_price = 4.00,
    sku = 'HD005',
    preparation_time = 4,
    calories = 5,
    tags = ARRAY['شاي', 'نعناع', 'منعش']
WHERE name = 'شاي بالنعناع' AND sku IS NULL;

UPDATE products SET 
    cost_price = 7.00,
    sku = 'HD006',
    preparation_time = 6,
    calories = 200,
    tags = ARRAY['شوكولاتة', 'كريمة', 'حلو']
WHERE name = 'هوت شوكلت' AND sku IS NULL;

-- تحديث المشروبات الباردة
UPDATE products SET 
    cost_price = 8.50,
    sku = 'CD001',
    preparation_time = 4,
    calories = 90,
    tags = ARRAY['قهوة', 'مثلج', 'حليب']
WHERE name = 'آيس كوفي' AND sku IS NULL;

UPDATE products SET 
    cost_price = 11.00,
    sku = 'CD002',
    preparation_time = 6,
    calories = 280,
    tags = ARRAY['فراب', 'قهوة', 'مخفوق']
WHERE name = 'فرابتشينو' AND sku IS NULL;

UPDATE products SET 
    cost_price = 6.00,
    sku = 'CD003',
    preparation_time = 3,
    calories = 110,
    tags = ARRAY['عصير', 'طبيعي', 'فيتامين سي']
WHERE name = 'عصير برتقال طازج' AND sku IS NULL;

UPDATE products SET 
    cost_price = 7.50,
    sku = 'CD004',
    preparation_time = 5,
    calories = 80,
    tags = ARRAY['منعش', 'نعناع', 'ليمون']
WHERE name = 'موهيتو' AND sku IS NULL;

UPDATE products SET 
    cost_price = 9.00,
    sku = 'CD005',
    preparation_time = 4,
    calories = 320,
    tags = ARRAY['ميلك شيك', 'فانيلا', 'كريمي']
WHERE name = 'ميلك شيك فانيلا' AND sku IS NULL;

-- تحديث الحلويات
UPDATE products SET 
    cost_price = 12.00,
    sku = 'DS001',
    preparation_time = 2,
    calories = 450,
    tags = ARRAY['حلوى', 'جبن', 'كريمي']
WHERE name = 'تشيز كيك' AND sku IS NULL;

UPDATE products SET 
    cost_price = 14.00,
    sku = 'DS002',
    preparation_time = 3,
    calories = 380,
    tags = ARRAY['حلوى', 'إيطالي', 'قهوة']
WHERE name = 'تيراميسو' AND sku IS NULL;

UPDATE products SET 
    cost_price = 10.00,
    sku = 'DS003',
    preparation_time = 5,
    calories = 320,
    tags = ARRAY['حلوى', 'شرقي', 'جبن']
WHERE name = 'كنافة' AND sku IS NULL;

-- تحديث الوجبات الخفيفة
UPDATE products SET 
    cost_price = 9.50,
    sku = 'LM001',
    preparation_time = 6,
    calories = 380,
    tags = ARRAY['ساندويتش', 'تونة', 'خضار']
WHERE name = 'ساندويتش تونة' AND sku IS NULL;

UPDATE products SET 
    cost_price = 12.00,
    sku = 'LM002',
    preparation_time = 8,
    calories = 520,
    tags = ARRAY['ساندويتش', 'دجاج', 'خضار']
WHERE name = 'كلوب ساندويتش' AND sku IS NULL;

UPDATE products SET 
    cost_price = 11.00,
    sku = 'LM003',
    preparation_time = 7,
    calories = 420,
    tags = ARRAY['سلطة', 'دجاج', 'صحي']
WHERE name = 'سلطة سيزر' AND sku IS NULL;

-- ========================================
-- INSERT INVENTORY DATA FOR EXISTING PRODUCTS
-- ========================================

-- إدراج بيانات المخزون للمنتجات الموجودة
INSERT INTO inventory (product_id, current_stock, minimum_stock, maximum_stock, unit_of_measure)
SELECT 
    p.id,
    CASE 
        WHEN p.sku LIKE 'HD%' THEN 100  -- Hot drinks ingredients
        WHEN p.sku LIKE 'CD%' THEN 80   -- Cold drinks ingredients
        WHEN p.sku LIKE 'DS%' THEN 20   -- Desserts
        WHEN p.sku LIKE 'PT%' THEN 30   -- Pastries
        WHEN p.sku LIKE 'LM%' THEN 25   -- Light meals ingredients
        ELSE 50
    END as current_stock,
    CASE 
        WHEN p.sku LIKE 'HD%' THEN 20
        WHEN p.sku LIKE 'CD%' THEN 15
        WHEN p.sku LIKE 'DS%' THEN 5
        WHEN p.sku LIKE 'PT%' THEN 10
        WHEN p.sku LIKE 'LM%' THEN 8
        ELSE 10
    END as minimum_stock,
    CASE 
        WHEN p.sku LIKE 'HD%' THEN 200
        WHEN p.sku LIKE 'CD%' THEN 150
        WHEN p.sku LIKE 'DS%' THEN 50
        WHEN p.sku LIKE 'PT%' THEN 80
        WHEN p.sku LIKE 'LM%' THEN 60
        ELSE 100
    END as maximum_stock,
    CASE 
        WHEN p.sku LIKE 'HD%' OR p.sku LIKE 'CD%' THEN 'serving'
        WHEN p.sku LIKE 'DS%' OR p.sku LIKE 'PT%' THEN 'piece'
        WHEN p.sku LIKE 'LM%' THEN 'portion'
        ELSE 'unit'
    END as unit_of_measure
FROM products p
WHERE p.sku IS NOT NULL
ON CONFLICT (product_id) DO UPDATE SET
    current_stock = EXCLUDED.current_stock,
    minimum_stock = EXCLUDED.minimum_stock,
    maximum_stock = EXCLUDED.maximum_stock,
    unit_of_measure = EXCLUDED.unit_of_measure,
    updated_at = NOW();

-- ========================================
-- UPDATE WAITERS WITH EMPLOYEE IDs
-- ========================================

-- تحديث النادلين بمعرفات الموظفين
UPDATE waiters SET 
    employee_id = 'EMP001',
    shift_start = '08:00',
    shift_end = '20:00',
    hourly_rate = 50.00,
    commission_rate = 10.0
WHERE user_id = 'c8e1f676-9e10-488f-914f-c238daa9b75c' AND employee_id IS NULL;

UPDATE waiters SET 
    employee_id = 'EMP002',
    shift_start = '08:00',
    shift_end = '16:00',
    hourly_rate = 25.00,
    commission_rate = 5.0
WHERE user_id = '82523602-ad24-4170-9ff1-5beb008f7f35' AND employee_id IS NULL;

UPDATE waiters SET 
    employee_id = 'EMP003',
    shift_start = '16:00',
    shift_end = '00:00',
    hourly_rate = 30.00,
    commission_rate = 7.5
WHERE user_id = 'b02c9cda-9c77-47ec-9d70-8186a3944997' AND employee_id IS NULL;

UPDATE waiters SET 
    employee_id = 'EMP004',
    shift_start = '00:00',
    shift_end = '08:00',
    hourly_rate = 25.00,
    commission_rate = 5.0
WHERE user_id = '7eb51a33-a1ab-4264-baf8-055641dbab5d' AND employee_id IS NULL;

-- ========================================
-- UPDATE ORDER NUMBERS FOR EXISTING ORDERS
-- ========================================

-- تحديث أرقام الطلبات الموجودة
UPDATE orders 
SET order_number = 'ORD-' || TO_CHAR(created_at, 'YYYYMMDD') || '-' || LPAD(ROW_NUMBER() OVER (ORDER BY created_at)::TEXT, 4, '0')
WHERE order_number IS NULL;

-- تحديث order_date للطلبات الموجودة
UPDATE orders 
SET order_date = DATE(created_at)
WHERE order_date IS NULL;

-- تحديث product_name في order_items
UPDATE order_items 
SET product_name = p.name 
FROM products p 
WHERE order_items.product_id = p.id 
AND order_items.product_name IS NULL;

-- ========================================
-- CREATE ADDITIONAL INDEXES
-- ========================================

-- إنشاء فهارس إضافية للأداء
CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku);
CREATE INDEX IF NOT EXISTS idx_products_tags ON products USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_inventory_current_stock ON inventory(current_stock);
CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders(order_number);
CREATE INDEX IF NOT EXISTS idx_orders_order_date ON orders(order_date);
CREATE INDEX IF NOT EXISTS idx_payments_order_id ON payments(order_id);
CREATE INDEX IF NOT EXISTS idx_order_status_history_order_id ON order_status_history(order_id);

-- ========================================
-- FINAL SUCCESS MESSAGE
-- ========================================

-- تم تحديث البيانات بنجاح! ✅
-- 
-- التحديثات المطبقة:
-- ✅ تحديث بيانات المنتجات (أسعار التكلفة، SKU، وقت التحضير، السعرات، العلامات)
-- ✅ إدراج بيانات المخزون
-- ✅ تحديث بيانات النادلين
-- ✅ تحديث أرقام الطلبات
-- ✅ إنشاء فهارس إضافية
-- 
-- يمكنك الآن تسجيل الدخول باستخدام:
-- المستخدم: aminovski10 | كلمة المرور: Mam@loka10
-- أو أي من المستخدمين الآخرين الموجودين
