-- إصلاح نهائي لمشكلة كلمات المرور
-- تشغيل هذا الملف في SQL Editor

-- ========================================
-- الخطوة 1: حذف جميع المستخدمين الموجودين وإعادة إنشائهم
-- ========================================

-- حذف المستخدمين الموجودين (احتياطي)
DELETE FROM users WHERE username IN ('admin', 'waiter', 'barista');

-- ========================================
-- الخطوة 2: تحديث المستخدمين الأصليين بكلمات مرور واضحة
-- ========================================

-- تحديث المستخدم الأصلي aminovski10
UPDATE users 
SET 
    password = 'Mam@loka10',
    user_type = 'admin',
    is_active = true,
    updated_at = NOW()
WHERE username = 'aminovski10';

-- تحديث المستخدم amine.harakat
UPDATE users 
SET 
    password = 'Mam@loka10',
    user_type = 'waiter',
    is_active = true,
    updated_at = NOW()
WHERE username = 'amine.harakat';

-- تحديث المستخدم beta2
UPDATE users 
SET 
    password = 'Mam@loka10',
    user_type = 'barista',
    is_active = true,
    updated_at = NOW()
WHERE username = 'beta2';

-- تحديث المستخدم enima10
UPDATE users 
SET 
    password = 'Mam@loka10',
    user_type = 'waiter',
    is_active = true,
    updated_at = NOW()
WHERE username = 'enima10';

-- ========================================
-- الخطوة 3: إضافة مستخدمين جدد للاختبار
-- ========================================

-- إضافة مستخدم admin بسيط
INSERT INTO users (
    id,
    username,
    email,
    password,
    full_name,
    user_type,
    is_active,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'admin',
    '<EMAIL>',
    'admin123',
    'مدير الاختبار',
    'admin',
    true,
    NOW(),
    NOW()
) ON CONFLICT (username) DO UPDATE SET
    password = 'admin123',
    user_type = 'admin',
    is_active = true,
    updated_at = NOW();

-- إضافة مستخدم waiter بسيط
INSERT INTO users (
    id,
    username,
    email,
    password,
    full_name,
    user_type,
    is_active,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'waiter',
    '<EMAIL>',
    'waiter123',
    'نادل الاختبار',
    'waiter',
    true,
    NOW(),
    NOW()
) ON CONFLICT (username) DO UPDATE SET
    password = 'waiter123',
    user_type = 'waiter',
    is_active = true,
    updated_at = NOW();

-- إضافة مستخدم barista بسيط
INSERT INTO users (
    id,
    username,
    email,
    password,
    full_name,
    user_type,
    is_active,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'barista',
    '<EMAIL>',
    'barista123',
    'باريستا الاختبار',
    'barista',
    true,
    NOW(),
    NOW()
) ON CONFLICT (username) DO UPDATE SET
    password = 'barista123',
    user_type = 'barista',
    is_active = true,
    updated_at = NOW();

-- ========================================
-- الخطوة 4: التحقق من النتائج النهائية
-- ========================================

-- عرض جميع المستخدمين مع كلمات المرور
SELECT 
    '🔍 جميع المستخدمين' as info,
    username,
    email,
    password,
    full_name,
    user_type,
    is_active
FROM users 
ORDER BY 
    CASE user_type 
        WHEN 'admin' THEN 1
        WHEN 'manager' THEN 2
        WHEN 'barista' THEN 3
        WHEN 'waiter' THEN 4
        ELSE 5
    END,
    username;

-- ========================================
-- الخطوة 5: اختبار تسجيل الدخول لكل مستخدم
-- ========================================

-- اختبار المستخدم الأصلي
SELECT 
    '✅ اختبار aminovski10' as test,
    username,
    password,
    CASE 
        WHEN password = 'Mam@loka10' THEN '✅ صحيح'
        ELSE '❌ خطأ'
    END as password_check
FROM users 
WHERE username = 'aminovski10';

-- اختبار مستخدم admin
SELECT 
    '✅ اختبار admin' as test,
    username,
    password,
    CASE 
        WHEN password = 'admin123' THEN '✅ صحيح'
        ELSE '❌ خطأ'
    END as password_check
FROM users 
WHERE username = 'admin';

-- اختبار مستخدم waiter
SELECT 
    '✅ اختبار waiter' as test,
    username,
    password,
    CASE 
        WHEN password = 'waiter123' THEN '✅ صحيح'
        ELSE '❌ خطأ'
    END as password_check
FROM users 
WHERE username = 'waiter';

-- اختبار مستخدم barista
SELECT 
    '✅ اختبار barista' as test,
    username,
    password,
    CASE 
        WHEN password = 'barista123' THEN '✅ صحيح'
        ELSE '❌ خطأ'
    END as password_check
FROM users 
WHERE username = 'barista';

-- ========================================
-- رسالة النجاح النهائية
-- ========================================

SELECT 
    '🎉 تم إصلاح كلمات المرور بنجاح!' as message,
    'يمكنك الآن تسجيل الدخول باستخدام:' as instructions;

SELECT 
    'المستخدمين المتاحين:' as available_users,
    username || ' / ' || password as credentials
FROM users 
WHERE is_active = true
ORDER BY user_type, username;
