-- Avie Cafe Enhanced Seed Data
-- تشغيل هذا الملف بعد إنشاء الجداول لإدراج البيانات الافتراضية المحسّنة

-- ========================================
-- INSERT PRODUCTS DATA
-- ========================================

-- Insert products for Hot Drinks category
INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'قهوة تركية', 'Turkish Coffee', 'قهوة بن عربي أصيلة مع الهيل', 'Authentic Arabic coffee with cardamom', 15.00, 8.00, '/images/turki.png', c.id, 'HD001', true, 8, 50, ARRAY['قهوة', 'تقليدي', 'هيل']
FROM categories c WHERE c.name = 'مشروبات ساخنة'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'إسبريسو', 'Espresso', 'قهوة مركزة بقوة 30 مل', 'Concentrated coffee shot 30ml', 12.00, 6.00, '/images/express.png', c.id, 'HD002', false, 3, 30, ARRAY['قهوة', 'مركز', 'إيطالي']
FROM categories c WHERE c.name = 'مشروبات ساخنة'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'كابتشينو', 'Cappuccino', 'إسبريسو مع حليب مبخر ورغوة', 'Espresso with steamed milk and foam', 18.00, 9.00, '/images/cappuccino.png', c.id, 'HD003', true, 5, 120, ARRAY['قهوة', 'حليب', 'رغوة']
FROM categories c WHERE c.name = 'مشروبات ساخنة'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'لاتيه', 'Latte', 'لاتيه قهوة مع حليب مبخر ورغوة', 'Coffee latte with steamed milk and foam', 18.00, 9.50, '/images/late-coffe.png', c.id, 'HD004', true, 5, 150, ARRAY['قهوة', 'حليب', 'كريمي']
FROM categories c WHERE c.name = 'مشروبات ساخنة'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'شاي بالنعناع', 'Mint Tea', 'شاي أخضر مع أوراق نعناع طازجة', 'Green tea with fresh mint leaves', 10.00, 4.00, '/images/tea-mint.png', c.id, 'HD005', false, 4, 5, ARRAY['شاي', 'نعناع', 'منعش']
FROM categories c WHERE c.name = 'مشروبات ساخنة'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'هوت شوكلت', 'Hot Chocolate', 'شوكولاتة ساخنة مع كريمة', 'Hot chocolate with whipped cream', 16.00, 7.00, '/images/hot-chocolate.webp', c.id, 'HD006', false, 6, 200, ARRAY['شوكولاتة', 'كريمة', 'حلو']
FROM categories c WHERE c.name = 'مشروبات ساخنة'
ON CONFLICT (sku) DO NOTHING;

-- Insert products for Cold Drinks category
INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'آيس كوفي', 'Iced Coffee', 'قهوة باردة مع الثلج والحليب', 'Cold coffee with ice and milk', 20.00, 8.50, '/images/iced-coffee.png', c.id, 'CD001', true, 4, 90, ARRAY['قهوة', 'مثلج', 'حليب']
FROM categories c WHERE c.name = 'مشروبات باردة'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'فرابتشينو', 'Frappuccino', 'مشروب قهوة مثلج مخفوق', 'Blended iced coffee drink', 25.00, 11.00, '/images/frappuccino.png', c.id, 'CD002', true, 6, 280, ARRAY['فراب', 'قهوة', 'مخفوق']
FROM categories c WHERE c.name = 'مشروبات باردة'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'عصير برتقال طازج', 'Fresh Orange Juice', 'عصير برتقال طبيعي 100%', '100% natural orange juice', 15.00, 6.00, '/images/orange-juice.png', c.id, 'CD003', false, 3, 110, ARRAY['عصير', 'طبيعي', 'فيتامين سي']
FROM categories c WHERE c.name = 'مشروبات باردة'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'موهيتو', 'Mojito', 'مشروب منعش بالنعناع والليمون', 'Refreshing drink with mint and lime', 18.00, 7.50, '/images/mojito.png', c.id, 'CD004', false, 5, 80, ARRAY['منعش', 'نعناع', 'ليمون']
FROM categories c WHERE c.name = 'مشروبات باردة'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'ميلك شيك فانيلا', 'Vanilla Milkshake', 'ميلك شيك كريمي بطعم الفانيلا', 'Creamy vanilla flavored milkshake', 22.00, 9.00, '/images/vanilla-milkshake.png', c.id, 'CD005', false, 4, 320, ARRAY['ميلك شيك', 'فانيلا', 'كريمي']
FROM categories c WHERE c.name = 'مشروبات باردة'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'عصير مانجو', 'Mango Juice', 'عصير مانجو طبيعي طازج', 'Fresh natural mango juice', 16.00, 7.00, '/images/mango-juice.png', c.id, 'CD006', true, 3, 140, ARRAY['عصير', 'مانجو', 'استوائي']
FROM categories c WHERE c.name = 'مشروبات باردة'
ON CONFLICT (sku) DO NOTHING;
-- Insert products for Desserts category
INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'تشيز كيك', 'Cheesecake', 'تشيز كيك كريمي لذيذ', 'Delicious creamy cheesecake', 28.00, 12.00, '/images/cheesecake.png', c.id, 'DS001', true, 2, 450, ARRAY['حلوى', 'جبن', 'كريمي']
FROM categories c WHERE c.name = 'حلويات'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'تيراميسو', 'Tiramisu', 'تيراميسو إيطالي أصيل', 'Authentic Italian tiramisu', 32.00, 14.00, '/images/tiramisu.png', c.id, 'DS002', true, 3, 380, ARRAY['حلوى', 'إيطالي', 'قهوة']
FROM categories c WHERE c.name = 'حلويات'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'كنافة', 'Kunafa', 'كنافة شرقية بالجبن', 'Oriental kunafa with cheese', 25.00, 10.00, '/images/kunafa.png', c.id, 'DS003', false, 5, 320, ARRAY['حلوى', 'شرقي', 'جبن']
FROM categories c WHERE c.name = 'حلويات'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'مهلبية', 'Muhallabia', 'مهلبية بالفستق والقرفة', 'Muhallabia with pistachios and cinnamon', 18.00, 7.50, '/images/muhallabia.png', c.id, 'DS004', false, 2, 220, ARRAY['حلوى', 'فستق', 'قرفة']
FROM categories c WHERE c.name = 'حلويات'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'براونيز', 'Brownies', 'براونيز بالشوكولاتة والجوز', 'Chocolate brownies with walnuts', 22.00, 9.00, '/images/brownies.png', c.id, 'DS005', true, 2, 350, ARRAY['حلوى', 'شوكولاتة', 'جوز']
FROM categories c WHERE c.name = 'حلويات'
ON CONFLICT (sku) DO NOTHING;

-- Insert products for Pastries category
INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'كرواسون', 'Croissant', 'كرواسون فرنسي طازج', 'Fresh French croissant', 12.00, 5.00, '/images/croissant.png', c.id, 'PT001', false, 3, 180, ARRAY['معجنات', 'فرنسي', 'زبدة']
FROM categories c WHERE c.name = 'معجنات'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'دونات', 'Donuts', 'دونات محلى بالسكر', 'Sugar glazed donuts', 8.00, 3.50, '/images/donuts.png', c.id, 'PT002', true, 2, 250, ARRAY['معجنات', 'حلو', 'سكر']
FROM categories c WHERE c.name = 'معجنات'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'مافن', 'Muffin', 'مافن بالتوت الأزرق', 'Blueberry muffin', 15.00, 6.50, '/images/muffin.png', c.id, 'PT003', false, 2, 280, ARRAY['معجنات', 'توت', 'إفطار']
FROM categories c WHERE c.name = 'معجنات'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'سينابون', 'Cinnamon Roll', 'لفائف القرفة بالكريمة', 'Cinnamon rolls with cream', 18.00, 7.50, '/images/cinnamon-roll.png', c.id, 'PT004', true, 3, 320, ARRAY['معجنات', 'قرفة', 'كريمة']
FROM categories c WHERE c.name = 'معجنات'
ON CONFLICT (sku) DO NOTHING;

-- Insert products for Light Meals category
INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'كلوب ساندويتش', 'Club Sandwich', 'كلوب ساندويتش بالدجاج', 'Chicken club sandwich', 28.00, 12.00, '/images/club-sandwich.png', c.id, 'LM001', true, 8, 520, ARRAY['ساندويتش', 'دجاج', 'خضار']
FROM categories c WHERE c.name = 'وجبات خفيفة'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'ساندويتش تونة', 'Tuna Sandwich', 'ساندويتش تونة بالخضار', 'Tuna sandwich with vegetables', 22.00, 9.50, '/images/tuna-sandwich.png', c.id, 'LM002', false, 6, 380, ARRAY['ساندويتش', 'تونة', 'خضار']
FROM categories c WHERE c.name = 'وجبات خفيفة'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'سلطة سيزر', 'Caesar Salad', 'سلطة سيزر بالدجاج المشوي', 'Caesar salad with grilled chicken', 26.00, 11.00, '/images/caesar-salad.png', c.id, 'LM003', false, 7, 420, ARRAY['سلطة', 'دجاج', 'صحي']
FROM categories c WHERE c.name = 'وجبات خفيفة'
ON CONFLICT (sku) DO NOTHING;

INSERT INTO products (name, name_en, description, description_en, price, cost_price, image_url, category_id, sku, is_popular, preparation_time, calories, tags)
SELECT
  'بانيني', 'Panini', 'بانيني بالجبن والطماطم', 'Panini with cheese and tomato', 20.00, 8.50, '/images/panini.png', c.id, 'LM004', true, 5, 350, ARRAY['بانيني', 'جبن', 'طماطم']
FROM categories c WHERE c.name = 'وجبات خفيفة'
ON CONFLICT (sku) DO NOTHING;
-- ========================================
-- INSERT INVENTORY DATA
-- ========================================

-- Insert inventory for all products
INSERT INTO inventory (product_id, current_stock, minimum_stock, maximum_stock, unit_of_measure)
SELECT
    p.id,
    CASE
        WHEN p.sku LIKE 'HD%' THEN 100  -- Hot drinks ingredients
        WHEN p.sku LIKE 'CD%' THEN 80   -- Cold drinks ingredients
        WHEN p.sku LIKE 'DS%' THEN 20   -- Desserts
        WHEN p.sku LIKE 'PT%' THEN 30   -- Pastries
        WHEN p.sku LIKE 'LM%' THEN 25   -- Light meals ingredients
        ELSE 50
    END as current_stock,
    CASE
        WHEN p.sku LIKE 'HD%' THEN 20
        WHEN p.sku LIKE 'CD%' THEN 15
        WHEN p.sku LIKE 'DS%' THEN 5
        WHEN p.sku LIKE 'PT%' THEN 10
        WHEN p.sku LIKE 'LM%' THEN 8
        ELSE 10
    END as minimum_stock,
    CASE
        WHEN p.sku LIKE 'HD%' THEN 200
        WHEN p.sku LIKE 'CD%' THEN 150
        WHEN p.sku LIKE 'DS%' THEN 50
        WHEN p.sku LIKE 'PT%' THEN 80
        WHEN p.sku LIKE 'LM%' THEN 60
        ELSE 100
    END as maximum_stock,
    CASE
        WHEN p.sku LIKE 'HD%' OR p.sku LIKE 'CD%' THEN 'serving'
        WHEN p.sku LIKE 'DS%' OR p.sku LIKE 'PT%' THEN 'piece'
        WHEN p.sku LIKE 'LM%' THEN 'portion'
        ELSE 'unit'
    END as unit_of_measure
FROM products p
WHERE p.sku IS NOT NULL
ON CONFLICT (product_id) DO NOTHING;

-- ========================================
-- INSERT SAMPLE ORDER DATA
-- ========================================

-- Insert a sample order
INSERT INTO orders (
    order_number,
    table_id,
    table_number,
    customer_name,
    customer_phone,
    notes,
    subtotal,
    tax_rate,
    tax_amount,
    total_amount,
    status,
    payment_status,
    assigned_waiter_id,
    assigned_waiter_name,
    order_type
)
SELECT
    'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-0001',
    t.id,
    t.table_number,
    'أحمد محمد',
    '+966501234567',
    'بدون سكر في القهوة',
    45.00,
    15.0,
    6.75,
    51.75,
    'completed',
    'paid',
    w.id,
    w.name,
    'dine_in'
FROM tables t, waiters w
WHERE t.table_number = 1 AND w.employee_id = 'EMP001'
LIMIT 1
ON CONFLICT (order_number) DO NOTHING;

-- Insert sample order items
INSERT INTO order_items (order_id, product_id, product_name, quantity, unit_price, total_price, status)
SELECT
    o.id,
    p.id,
    p.name,
    2,
    p.price,
    p.price * 2,
    'served'
FROM orders o, products p
WHERE o.order_number LIKE 'ORD-%0001' AND p.sku = 'HD003'  -- Cappuccino
LIMIT 1
ON CONFLICT DO NOTHING;

INSERT INTO order_items (order_id, product_id, product_name, quantity, unit_price, total_price, status)
SELECT
    o.id,
    p.id,
    p.name,
    1,
    p.price,
    p.price,
    'served'
FROM orders o, products p
WHERE o.order_number LIKE 'ORD-%0001' AND p.sku = 'DS005'  -- Brownies
LIMIT 1
ON CONFLICT DO NOTHING;

-- Insert sample payment
INSERT INTO payments (order_id, amount, payment_method, payment_status, processed_by_name)
SELECT
    o.id,
    o.total_amount,
    'cash',
    'completed',
    'كاشير 1'
FROM orders o
WHERE o.order_number LIKE 'ORD-%0001'
LIMIT 1
ON CONFLICT DO NOTHING;

-- Insert sample sales record
INSERT INTO sales (
    order_id,
    waiter_id,
    waiter_name,
    cashier_name,
    gross_amount,
    tax_amount,
    net_amount,
    payment_method,
    commission_amount
)
SELECT
    o.id,
    o.assigned_waiter_id,
    o.assigned_waiter_name,
    'كاشير 1',
    o.subtotal,
    o.tax_amount,
    o.total_amount,
    'cash',
    ROUND(o.subtotal * 0.05, 2)  -- 5% commission
FROM orders o
WHERE o.order_number LIKE 'ORD-%0001'
LIMIT 1
ON CONFLICT DO NOTHING;

-- ========================================
-- FINAL NOTES AND INSTRUCTIONS
-- ========================================

-- تم إنشاء قاعدة البيانات بنجاح!
--
-- الجداول المُنشأة:
-- 1. users - المستخدمين والموظفين
-- 2. categories - فئات المنتجات
-- 3. products - المنتجات والأصناف
-- 4. waiters - النادلين
-- 5. tables - طاولات المطعم
-- 6. orders - الطلبات
-- 7. order_items - عناصر الطلبات
-- 8. payments - المدفوعات
-- 9. sales - المبيعات والتقارير
-- 10. inventory - المخزون
-- 11. order_status_history - تاريخ حالات الطلبات
--
-- المميزات المضافة:
-- ✅ Row Level Security (RLS) مفعل
-- ✅ Triggers للتحديث التلقائي
-- ✅ Functions للحسابات
-- ✅ Indexes للأداء
-- ✅ بيانات نموذجية شاملة
-- ✅ دعم متعدد اللغات (عربي/إنجليزي)
-- ✅ تتبع المخزون
-- ✅ نظام العمولات
-- ✅ تاريخ تغيير الحالات
  'تشيز كيك', 'Cheesecake', 'تشيز كيك كريمي بالفراولة', 25.00, '/images/cheesecake.png', c.id, true
FROM categories c WHERE c.name = 'حلويات'
ON CONFLICT DO NOTHING;

INSERT INTO products (name, name_en, description, price, image_url, category_id, is_popular)
SELECT
  'تيراميسو', 'Tiramisu', 'حلوى إيطالية بالقهوة والماسكاربوني', 28.00, '/images/tiramisu.png', c.id, true
FROM categories c WHERE c.name = 'حلويات'
ON CONFLICT DO NOTHING;

INSERT INTO products (name, name_en, description, price, image_url, category_id, is_popular)
SELECT
  'براونيز', 'Brownies', 'براونيز شوكولاتة غني ولذيذ', 20.00, '/images/brownies.png', c.id, false
FROM categories c WHERE c.name = 'حلويات'
ON CONFLICT DO NOTHING;

INSERT INTO products (name, name_en, description, price, image_url, category_id, is_popular)
SELECT
  'آيس كريم', 'Ice Cream', 'آيس كريم بنكهات متنوعة', 15.00, '/images/ice-cream.png', c.id, false
FROM categories c WHERE c.name = 'حلويات'
ON CONFLICT DO NOTHING;

-- Insert products for Pastries category
INSERT INTO products (name, name_en, description, price, image_url, category_id, is_popular)
SELECT
  'كرواسون', 'Croissant', 'كرواسون زبدة طازج', 12.00, '/images/croissant.png', c.id, true
FROM categories c WHERE c.name = 'معجنات'
ON CONFLICT DO NOTHING;

INSERT INTO products (name, name_en, description, price, image_url, category_id, is_popular)
SELECT
  'مافن بلوبيري', 'Blueberry Muffin', 'مافن طازج بالتوت الأزرق', 15.00, '/images/blueberry-muffin.png', c.id, false
FROM categories c WHERE c.name = 'معجنات'
ON CONFLICT DO NOTHING;

INSERT INTO products (name, name_en, description, price, image_url, category_id, is_popular)
SELECT
  'دونات', 'Donuts', 'دونات محلى بالسكر والشوكولاتة', 10.00, '/images/donuts.png', c.id, false
FROM categories c WHERE c.name = 'معجنات'
ON CONFLICT DO NOTHING;

INSERT INTO products (name, name_en, description, price, image_url, category_id, is_popular)
SELECT
  'سينامون رول', 'Cinnamon Roll', 'لفائف القرفة الطازجة', 18.00, '/images/cinnamon-roll.png', c.id, false
FROM categories c WHERE c.name = 'معجنات'
ON CONFLICT DO NOTHING;

-- Insert products for Snacks category
INSERT INTO products (name, name_en, description, price, image_url, category_id, is_popular)
SELECT
  'ساندويش كلوب', 'Club Sandwich', 'ساندويش كلوب بالدجاج والخضار', 25.00, '/images/club-sandwich.png', c.id, true
FROM categories c WHERE c.name = 'وجبات خفيفة'
ON CONFLICT DO NOTHING;

INSERT INTO products (name, name_en, description, price, image_url, category_id, is_popular)
SELECT
  'سلطة سيزر', 'Caesar Salad', 'سلطة سيزر بالدجاج المشوي', 22.00, '/images/caesar-salad.png', c.id, false
FROM categories c WHERE c.name = 'وجبات خفيفة'
ON CONFLICT DO NOTHING;

INSERT INTO products (name, name_en, description, price, image_url, category_id, is_popular)
SELECT
  'بانيني', 'Panini', 'بانيني مشوي بالجبن والطماطم', 20.00, '/images/panini.png', c.id, false
FROM categories c WHERE c.name = 'وجبات خفيفة'
ON CONFLICT DO NOTHING;

INSERT INTO products (name, name_en, description, price, image_url, category_id, is_popular)
SELECT
  'ناتشوز', 'Nachos', 'ناتشوز بالجبن والصلصة الحارة', 18.00, '/images/nachos.png', c.id, false
FROM categories c WHERE c.name = 'وجبات خفيفة'
ON CONFLICT DO NOTHING;
