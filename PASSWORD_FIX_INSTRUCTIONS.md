# حل مشكلة كلمة المرور غير الصحيحة

## 🔍 المشكلة
عند تسجيل الدخول يظهر "كلمة المرور غير صحيحة" رغم أن كلمة المرور صحيحة.

## 🎯 السبب
كلمات المرور في قاعدة البيانات مختلفة عن المتوقع.

## ⚡ الحل السريع (دقيقتان)

### الخطوة 1: فحص كلمات المرور الحالية
1. اذهب إلى **Supabase Dashboard** → **SQL Editor**
2. انسخ والصق محتوى ملف `check-passwords.sql`
3. اضغط **Run**
4. ستظهر كلمات المرور الفعلية

### الخطوة 2: إصلاح كلمات المرور
1. في **SQL Editor**
2. انسخ والصق محتوى ملف `fix-passwords-final.sql`
3. اضغط **Run**
4. ستظهر رسالة نجاح مع المستخدمين المتاحين

### الخطوة 3: تسجيل الدخول
بعد تشغيل الملف، استخدم هذه البيانات:

#### المستخدم الأصلي:
- **اسم المستخدم**: `aminovski10`
- **كلمة المرور**: `Mam@loka10`
- **النوع**: مدير

#### مستخدمين الاختبار:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **النوع**: مدير

- **اسم المستخدم**: `waiter`
- **كلمة المرور**: `waiter123`
- **النوع**: نادل

- **اسم المستخدم**: `barista`
- **كلمة المرور**: `barista123`
- **النوع**: باريستا

---

## 🔧 إذا لم يعمل الحل

### تحقق من Console في المتصفح:
1. اضغط **F12**
2. اذهب إلى **Console**
3. جرب تسجيل الدخول
4. ابحث عن رسائل الخطأ

### اختبار مباشر في Console:
```javascript
// اختبار الاتصال
fetch('https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/users?username=eq.aminovski10&select=*', {
    headers: {
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw'
    }
})
.then(r => r.json())
.then(data => console.log('بيانات المستخدم:', data));
```

---

## 🛠️ حلول إضافية

### الحل 1: إعادة تعيين كلمة مرور مستخدم واحد
```sql
UPDATE users 
SET password = 'كلمة_المرور_الجديدة'
WHERE username = 'اسم_المستخدم';
```

### الحل 2: إنشاء مستخدم جديد تماماً
```sql
INSERT INTO users (username, email, password, full_name, user_type) 
VALUES ('testuser', '<EMAIL>', 'test123', 'Test User', 'admin');
```

### الحل 3: فحص المشكلة في الكود
تحقق من ملف `src/pages/Login.jsx` في السطر الذي يقارن كلمة المرور:
```javascript
if (userData.password !== formData.password) {
    setError("كلمة المرور غير صحيحة");
    return;
}
```

---

## ✅ علامات النجاح

عندما يعمل الحل:
1. ستظهر في SQL Editor: "تم إصلاح كلمات المرور بنجاح!"
2. ستظهر قائمة بجميع المستخدمين وكلمات مرورهم
3. ستتمكن من تسجيل الدخول بنجاح
4. ستنتقل إلى لوحة التحكم

---

## 🔒 ملاحظة أمنية

هذا الحل للاختبار فقط. في الإنتاج:
- استخدم bcrypt أو argon2 لتشفير كلمات المرور
- لا تحفظ كلمات المرور كنص واضح
- استخدم Supabase Auth للمصادقة الآمنة

---

## 📞 إذا استمرت المشكلة

أرسل لي:
1. نتيجة تشغيل `check-passwords.sql`
2. رسائل الخطأ من Console
3. لقطة شاشة من محاولة تسجيل الدخول
