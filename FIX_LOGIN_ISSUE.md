# حل مشكلة تسجيل الدخول - مقهى آفي

## المشكلة
عند محاولة تسجيل الدخول، لا يتم جلب البيانات من Supabase بشكل صحيح.

## السبب
هناك عدة أسباب محتملة:
1. **عدم تطابق بنية الجداول**: الجداول الجديدة تختلف عن الجداول القديمة
2. **صلاحيات RLS**: قد تكون صلاحيات Row Level Security تمنع الوصول
3. **بيانات مفقودة**: بعض الأعمدة المطلوبة غير موجودة

## الحل المرحلي (خطوات سريعة)

### الخطوة 1: تشغيل ملف التحديث
```sql
-- في SQL Editor في Supabase
-- انسخ والصق محتوى ملف supabase-update-existing.sql
```

### الخطوة 2: تحديث بيانات المنتجات
```sql
-- في SQL Editor في Supabase
-- انسخ والصق محتوى ملف update-products-data.sql
```

### الخطوة 3: إيقاف RLS مؤقتاً للاختبار
```sql
-- إيقاف RLS مؤقتاً لجدول users للاختبار
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
```

### الخطوة 4: اختبار تسجيل الدخول
استخدم أحد هذه البيانات للاختبار:

**المدير:**
- اسم المستخدم: `aminovski10`
- كلمة المرور: `Mam@loka10`

**النادل:**
- اسم المستخدم: `amine.harakat`
- كلمة المرور: `Mam@loka10`

**الباريستا:**
- اسم المستخدم: `beta2`
- كلمة المرور: `Mam@loka10`

## الحل الدائم

### 1. تحديث صلاحيات RLS
```sql
-- حذف السياسات القديمة
DROP POLICY IF EXISTS "Users can view their own profile" ON users;
DROP POLICY IF EXISTS "Users can update their own profile" ON users;

-- إنشاء سياسات جديدة للمصادقة
CREATE POLICY "Allow login access" ON users
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated users to read users" ON users
    FOR SELECT USING (auth.role() = 'authenticated');

-- إعادة تفعيل RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
```

### 2. التحقق من الاتصال
```sql
-- اختبار الاتصال بقاعدة البيانات
SELECT 
    u.id,
    u.username,
    u.email,
    u.full_name,
    u.user_type,
    u.is_active
FROM users u
WHERE u.username = 'aminovski10';
```

### 3. فحص البيانات
```sql
-- فحص جميع المستخدمين
SELECT 
    username,
    email,
    full_name,
    user_type,
    is_active,
    created_at
FROM users
ORDER BY created_at DESC;
```

## استكشاف الأخطاء

### مشكلة: "البريد الإلكتروني أو اسم المستخدم غير موجود"
```sql
-- التحقق من وجود المستخدم
SELECT * FROM users WHERE username = 'aminovski10' OR email = '<EMAIL>';
```

### مشكلة: "كلمة المرور غير صحيحة"
```sql
-- التحقق من كلمة المرور (للاختبار فقط)
SELECT username, password FROM users WHERE username = 'aminovski10';
```

### مشكلة: خطأ في الاتصال بقاعدة البيانات
1. تحقق من إعدادات Supabase في `src/lib/supabase.js`
2. تأكد من صحة URL و API Key
3. تحقق من حالة الشبكة

## اختبار سريع في المتصفح

افتح Developer Tools (F12) وشغل هذا الكود في Console:

```javascript
// اختبار الاتصال بـ Supabase
import { supabase } from './src/lib/supabase.js';

// اختبار جلب المستخدمين
supabase
  .from('users')
  .select('*')
  .eq('username', 'aminovski10')
  .then(result => {
    console.log('نتيجة الاختبار:', result);
  })
  .catch(error => {
    console.error('خطأ في الاختبار:', error);
  });
```

## إعدادات Supabase المطلوبة

تأكد من أن هذه الإعدادات صحيحة في لوحة تحكم Supabase:

### 1. Authentication Settings
- Enable email confirmations: **OFF** (للاختبار)
- Enable phone confirmations: **OFF**

### 2. Database Settings
- Row Level Security: **Enabled** مع السياسات الصحيحة

### 3. API Settings
- تأكد من أن API Keys صحيحة ونشطة

## الخطوات التالية

بعد حل مشكلة تسجيل الدخول:

1. **اختبار جميع الوظائف**: تأكد من عمل جميع أجزاء التطبيق
2. **تحسين الأمان**: إعادة تفعيل RLS مع السياسات المناسبة
3. **إضافة المزيد من البيانات**: منتجات، طلبات، إلخ
4. **اختبار الأداء**: تحسين الاستعلامات والفهارس

## دعم إضافي

إذا استمرت المشكلة:

1. تحقق من logs في Supabase Dashboard
2. راجع Network tab في Developer Tools
3. تأكد من إعدادات CORS
4. اختبر الاتصال من خارج التطبيق

---

**ملاحظة مهمة**: هذا الحل مؤقت للاختبار. في الإنتاج، يجب استخدام نظام مصادقة آمن مع تشفير كلمات المرور وسياسات RLS محكمة.
