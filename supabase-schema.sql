-- Avie Cafe Database Schema - Complete Enhanced Version
-- تشغيل هذا الملف في SQL Editor في لوحة تحكم Supabase

-- ========================================
-- STEP 1: DROP EXISTING TABLES AND POLICIES
-- ========================================

-- Drop all existing policies
DO $$
DECLARE
    r RECORD;
BEGIN
    -- Drop all policies on all tables
    FOR r IN (SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public')
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || r.policyname || '" ON ' || r.schemaname || '.' || r.tablename;
    END LOOP;
END $$;

-- Drop existing tables in correct order (child tables first)
DROP TABLE IF EXISTS order_status_history CASCADE;
DROP TABLE IF EXISTS inventory CASCADE;
DROP TABLE IF EXISTS sales CASCADE;
DROP TABLE IF EXISTS payments CASCADE;
DROP TABLE IF EXISTS order_items CASCADE;
DROP TABLE IF EXISTS orders CASCADE;
DROP TABLE IF EXISTS tables CASCADE;
DROP TABLE IF EXISTS waiters CASCADE;
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS categories CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Drop existing functions and sequences
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS generate_order_number() CASCADE;
DROP FUNCTION IF EXISTS calculate_order_totals() CASCADE;
DROP FUNCTION IF EXISTS track_order_status_change() CASCADE;
DROP SEQUENCE IF EXISTS order_number_seq CASCADE;

-- ========================================
-- STEP 2: ENABLE EXTENSIONS
-- ========================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ========================================
-- STEP 3: CREATE ENHANCED TABLES
-- ========================================

-- Create users table (base table for authentication)
CREATE TABLE users (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  username VARCHAR(50) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL, -- استخدام password بدلاً من password_hash للتوافق
  full_name VARCHAR(200) NOT NULL,
  phone VARCHAR(20),
  user_type VARCHAR(20) NOT NULL DEFAULT 'waiter' -- استخدام user_type بدلاً من role للتوافق
    CHECK (user_type IN ('admin', 'manager', 'barista', 'waiter', 'cashier')),
  is_active BOOLEAN DEFAULT TRUE,
  last_login TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create categories table
CREATE TABLE categories (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  name_en VARCHAR(100),
  description TEXT,
  description_en TEXT,
  image_url TEXT,
  color_code VARCHAR(7) DEFAULT '#3B82F6', -- Hex color for UI
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Constraints
  CONSTRAINT categories_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
  CONSTRAINT categories_display_order_positive CHECK (display_order >= 0)
);

-- Create products table
CREATE TABLE products (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  name_en VARCHAR(200),
  description TEXT,
  description_en TEXT,
  price DECIMAL(10,2) NOT NULL,
  cost_price DECIMAL(10,2), -- For profit calculation
  image_url TEXT,
  category_id UUID NOT NULL REFERENCES categories(id) ON DELETE RESTRICT,
  sku VARCHAR(50) UNIQUE, -- Stock Keeping Unit
  barcode VARCHAR(100),
  is_popular BOOLEAN DEFAULT FALSE,
  is_available BOOLEAN DEFAULT TRUE,
  preparation_time INTEGER DEFAULT 5, -- in minutes
  calories INTEGER,
  allergens TEXT[], -- Array of allergens
  tags TEXT[], -- Array of tags for filtering
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Constraints
  CONSTRAINT products_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
  CONSTRAINT products_price_positive CHECK (price > 0),
  CONSTRAINT products_cost_price_positive CHECK (cost_price IS NULL OR cost_price >= 0),
  CONSTRAINT products_preparation_time_positive CHECK (preparation_time > 0)
);

-- Create waiters table
CREATE TABLE waiters (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(200) NOT NULL,
  employee_id VARCHAR(20) UNIQUE,
  shift_start TIME,
  shift_end TIME,
  hourly_rate DECIMAL(8,2),
  commission_rate DECIMAL(5,2) DEFAULT 0, -- Percentage
  is_active BOOLEAN DEFAULT TRUE,
  current_status VARCHAR(20) DEFAULT 'offline'
    CHECK (current_status IN ('online', 'offline', 'busy', 'break')),
  login_time TIMESTAMP WITH TIME ZONE,
  logout_time TIMESTAMP WITH TIME ZONE,
  total_orders_today INTEGER DEFAULT 0,
  total_sales_today DECIMAL(10,2) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Constraints
  CONSTRAINT waiters_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
  CONSTRAINT waiters_hourly_rate_positive CHECK (hourly_rate IS NULL OR hourly_rate > 0),
  CONSTRAINT waiters_commission_rate_valid CHECK (commission_rate >= 0 AND commission_rate <= 100)
);

-- Create tables table (for restaurant table management)
CREATE TABLE tables (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  table_number INTEGER UNIQUE NOT NULL,
  table_name VARCHAR(50),
  capacity INTEGER NOT NULL DEFAULT 4,
  location VARCHAR(100), -- e.g., 'Indoor', 'Outdoor', 'VIP'
  status VARCHAR(20) DEFAULT 'available'
    CHECK (status IN ('available', 'occupied', 'reserved', 'maintenance')),
  qr_code TEXT, -- QR code for digital menu
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Constraints
  CONSTRAINT tables_number_positive CHECK (table_number > 0),
  CONSTRAINT tables_capacity_positive CHECK (capacity > 0)
);

-- Create orders table
CREATE TABLE orders (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  order_number VARCHAR(20) UNIQUE NOT NULL, -- Human readable order number
  table_id UUID REFERENCES tables(id) ON DELETE SET NULL,
  table_number INTEGER, -- Keep for backward compatibility
  customer_name VARCHAR(200),
  customer_phone VARCHAR(20),
  customer_email VARCHAR(255),
  notes TEXT,
  special_requests TEXT,
  twitter_handle VARCHAR(100),
  subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
  discount_amount DECIMAL(10,2) DEFAULT 0,
  tax_rate DECIMAL(5,2) DEFAULT 15, -- Tax percentage
  tax_amount DECIMAL(10,2) DEFAULT 0,
  service_charge DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  status VARCHAR(20) NOT NULL DEFAULT 'pending'
    CHECK (status IN ('pending', 'confirmed', 'preparing', 'ready', 'served', 'completed', 'cancelled', 'refunded')),
  payment_status VARCHAR(20) DEFAULT 'unpaid'
    CHECK (payment_status IN ('unpaid', 'partial', 'paid', 'refunded')),
  payment_method VARCHAR(50),
  assigned_waiter_id UUID REFERENCES waiters(id) ON DELETE SET NULL,
  assigned_waiter_name VARCHAR(200), -- Denormalized for reporting
  order_type VARCHAR(20) DEFAULT 'dine_in'
    CHECK (order_type IN ('dine_in', 'takeaway', 'delivery')),
  estimated_prep_time INTEGER, -- in minutes
  actual_prep_time INTEGER, -- in minutes
  order_date DATE NOT NULL DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,

  -- Constraints
  CONSTRAINT orders_subtotal_positive CHECK (subtotal >= 0),
  CONSTRAINT orders_total_positive CHECK (total_amount >= 0),
  CONSTRAINT orders_discount_valid CHECK (discount_amount >= 0 AND discount_amount <= subtotal),
  CONSTRAINT orders_tax_rate_valid CHECK (tax_rate >= 0 AND tax_rate <= 100)
);
-- Create order_items table
CREATE TABLE order_items (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE RESTRICT,
  product_name VARCHAR(200) NOT NULL, -- Denormalized for history
  quantity INTEGER NOT NULL DEFAULT 1,
  unit_price DECIMAL(10,2) NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  special_instructions TEXT,
  status VARCHAR(20) DEFAULT 'pending'
    CHECK (status IN ('pending', 'confirmed', 'preparing', 'ready', 'served', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Constraints
  CONSTRAINT order_items_quantity_positive CHECK (quantity > 0),
  CONSTRAINT order_items_unit_price_positive CHECK (unit_price > 0),
  CONSTRAINT order_items_total_price_positive CHECK (total_price > 0)
);

-- Create payments table
CREATE TABLE payments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  payment_method VARCHAR(50) NOT NULL
    CHECK (payment_method IN ('cash', 'card', 'mobile', 'bank_transfer', 'voucher')),
  payment_status VARCHAR(20) DEFAULT 'completed'
    CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded')),
  transaction_id VARCHAR(100),
  reference_number VARCHAR(100),
  processed_by_id UUID REFERENCES users(id) ON DELETE SET NULL,
  processed_by_name VARCHAR(200),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Constraints
  CONSTRAINT payments_amount_positive CHECK (amount > 0)
);

-- Create sales table (for reporting and analytics)
CREATE TABLE sales (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  waiter_id UUID REFERENCES waiters(id) ON DELETE SET NULL,
  waiter_name VARCHAR(200) NOT NULL,
  cashier_id UUID REFERENCES users(id) ON DELETE SET NULL,
  cashier_name VARCHAR(200),
  gross_amount DECIMAL(10,2) NOT NULL,
  discount_amount DECIMAL(10,2) DEFAULT 0,
  tax_amount DECIMAL(10,2) DEFAULT 0,
  net_amount DECIMAL(10,2) NOT NULL,
  payment_method VARCHAR(50) NOT NULL,
  commission_amount DECIMAL(10,2) DEFAULT 0,
  sale_date DATE NOT NULL DEFAULT CURRENT_DATE,
  sale_time TIME NOT NULL DEFAULT CURRENT_TIME,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Constraints
  CONSTRAINT sales_gross_amount_positive CHECK (gross_amount > 0),
  CONSTRAINT sales_net_amount_positive CHECK (net_amount > 0),
  CONSTRAINT sales_discount_valid CHECK (discount_amount >= 0)
);

-- Create inventory table (for stock management)
CREATE TABLE inventory (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  current_stock INTEGER NOT NULL DEFAULT 0,
  minimum_stock INTEGER DEFAULT 0,
  maximum_stock INTEGER,
  unit_of_measure VARCHAR(20) DEFAULT 'piece',
  last_restocked_at TIMESTAMP WITH TIME ZONE,
  last_restocked_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Constraints
  CONSTRAINT inventory_current_stock_positive CHECK (current_stock >= 0),
  CONSTRAINT inventory_minimum_stock_positive CHECK (minimum_stock >= 0),
  CONSTRAINT inventory_maximum_stock_valid CHECK (maximum_stock IS NULL OR maximum_stock >= minimum_stock)
);

-- Create order_status_history table (for tracking order status changes)
CREATE TABLE order_status_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  old_status VARCHAR(20),
  new_status VARCHAR(20) NOT NULL,
  changed_by_id UUID REFERENCES users(id) ON DELETE SET NULL,
  changed_by_name VARCHAR(200),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================
-- STEP 4: CREATE FUNCTIONS AND TRIGGERS
-- ========================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to generate order number
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL THEN
        NEW.order_number := 'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD(NEXTVAL('order_number_seq')::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for order numbers
CREATE SEQUENCE IF NOT EXISTS order_number_seq START 1;

-- Function to calculate order totals
CREATE OR REPLACE FUNCTION calculate_order_totals()
RETURNS TRIGGER AS $$
DECLARE
    order_subtotal DECIMAL(10,2);
    order_tax DECIMAL(10,2);
    order_total DECIMAL(10,2);
BEGIN
    -- Calculate subtotal from order items
    SELECT COALESCE(SUM(total_price), 0) INTO order_subtotal
    FROM order_items
    WHERE order_id = NEW.order_id;

    -- Update order totals
    UPDATE orders
    SET
        subtotal = order_subtotal,
        tax_amount = ROUND(order_subtotal * tax_rate / 100, 2),
        total_amount = order_subtotal + ROUND(order_subtotal * tax_rate / 100, 2) + COALESCE(service_charge, 0) - COALESCE(discount_amount, 0)
    WHERE id = NEW.order_id;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to track order status changes
CREATE OR REPLACE FUNCTION track_order_status_change()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO order_status_history (order_id, old_status, new_status, changed_by_name, notes)
        VALUES (NEW.id, OLD.status, NEW.status, NEW.assigned_waiter_name, 'Status changed automatically');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- ========================================
-- STEP 5: CREATE TRIGGERS
-- ========================================

-- Triggers for updated_at columns
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_waiters_updated_at BEFORE UPDATE ON waiters
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tables_updated_at BEFORE UPDATE ON tables
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_order_items_updated_at BEFORE UPDATE ON order_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_inventory_updated_at BEFORE UPDATE ON inventory
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger for order number generation
CREATE TRIGGER generate_order_number_trigger BEFORE INSERT ON orders
    FOR EACH ROW EXECUTE FUNCTION generate_order_number();

-- Trigger for order total calculation
CREATE TRIGGER calculate_order_totals_trigger AFTER INSERT OR UPDATE OR DELETE ON order_items
    FOR EACH ROW EXECUTE FUNCTION calculate_order_totals();

-- Trigger for order status tracking
CREATE TRIGGER track_order_status_change_trigger AFTER UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION track_order_status_change();

-- ========================================
-- STEP 6: CREATE INDEXES FOR PERFORMANCE
-- ========================================

-- Users table indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

-- Categories table indexes
CREATE INDEX IF NOT EXISTS idx_categories_is_active ON categories(is_active);
CREATE INDEX IF NOT EXISTS idx_categories_display_order ON categories(display_order);

-- Products table indexes
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_is_available ON products(is_available);
CREATE INDEX IF NOT EXISTS idx_products_is_popular ON products(is_popular);
CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);

-- Waiters table indexes
CREATE INDEX IF NOT EXISTS idx_waiters_user_id ON waiters(user_id);
CREATE INDEX IF NOT EXISTS idx_waiters_is_active ON waiters(is_active);
CREATE INDEX IF NOT EXISTS idx_waiters_current_status ON waiters(current_status);
CREATE INDEX IF NOT EXISTS idx_waiters_employee_id ON waiters(employee_id);

-- Tables table indexes
CREATE INDEX IF NOT EXISTS idx_tables_table_number ON tables(table_number);
CREATE INDEX IF NOT EXISTS idx_tables_status ON tables(status);
CREATE INDEX IF NOT EXISTS idx_tables_is_active ON tables(is_active);

-- Orders table indexes
CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders(order_number);
CREATE INDEX IF NOT EXISTS idx_orders_table_id ON orders(table_id);
CREATE INDEX IF NOT EXISTS idx_orders_table_number ON orders(table_number);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);
CREATE INDEX IF NOT EXISTS idx_orders_assigned_waiter_id ON orders(assigned_waiter_id);
CREATE INDEX IF NOT EXISTS idx_orders_order_date ON orders(order_date);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_order_type ON orders(order_type);

-- Order items table indexes
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);
CREATE INDEX IF NOT EXISTS idx_order_items_status ON order_items(status);

-- Payments table indexes
CREATE INDEX IF NOT EXISTS idx_payments_order_id ON payments(order_id);
CREATE INDEX IF NOT EXISTS idx_payments_payment_method ON payments(payment_method);
CREATE INDEX IF NOT EXISTS idx_payments_payment_status ON payments(payment_status);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

-- Sales table indexes
CREATE INDEX IF NOT EXISTS idx_sales_order_id ON sales(order_id);
CREATE INDEX IF NOT EXISTS idx_sales_waiter_id ON sales(waiter_id);
CREATE INDEX IF NOT EXISTS idx_sales_sale_date ON sales(sale_date);
CREATE INDEX IF NOT EXISTS idx_sales_payment_method ON sales(payment_method);
CREATE INDEX IF NOT EXISTS idx_sales_created_at ON sales(created_at);

-- Inventory table indexes
CREATE INDEX IF NOT EXISTS idx_inventory_product_id ON inventory(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_current_stock ON inventory(current_stock);

-- Order status history table indexes
CREATE INDEX IF NOT EXISTS idx_order_status_history_order_id ON order_status_history(order_id);
CREATE INDEX IF NOT EXISTS idx_order_status_history_created_at ON order_status_history(created_at);
-- ========================================
-- STEP 7: ENABLE ROW LEVEL SECURITY (RLS)
-- ========================================
-- STEP 7: ENABLE ROW LEVEL SECURITY (RLS)
-- ========================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE waiters ENABLE ROW LEVEL SECURITY;
ALTER TABLE tables ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_status_history ENABLE ROW LEVEL SECURITY;

-- ========================================
-- STEP 8: CREATE RLS POLICIES
-- ========================================

-- Categories policies (public read access)
CREATE POLICY "Allow public read access to active categories" ON categories
    FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users full access to categories" ON categories
    FOR ALL USING (auth.role() = 'authenticated');

-- Products policies (public read access for available products)
CREATE POLICY "Allow public read access to available products" ON products
    FOR SELECT USING (is_available = true);

CREATE POLICY "Allow authenticated users full access to products" ON products
    FOR ALL USING (auth.role() = 'authenticated');

-- Users policies (restricted access)
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid()::text = id::text);

-- Waiters policies
CREATE POLICY "Allow authenticated read access to active waiters" ON waiters
    FOR SELECT USING (auth.role() = 'authenticated' AND is_active = true);

CREATE POLICY "Allow authenticated insert/update access to waiters" ON waiters
    FOR ALL USING (auth.role() = 'authenticated');

-- Tables policies
CREATE POLICY "Allow public read access to active tables" ON tables
    FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated full access to tables" ON tables
    FOR ALL USING (auth.role() = 'authenticated');

-- Orders policies
CREATE POLICY "Allow public insert access to orders" ON orders
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public read access to orders" ON orders
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated full access to orders" ON orders
    FOR ALL USING (auth.role() = 'authenticated');

-- Order items policies
CREATE POLICY "Allow public insert access to order_items" ON order_items
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public read access to order_items" ON order_items
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated full access to order_items" ON order_items
    FOR ALL USING (auth.role() = 'authenticated');

-- Payments policies
CREATE POLICY "Allow authenticated full access to payments" ON payments
    FOR ALL USING (auth.role() = 'authenticated');

-- Sales policies
CREATE POLICY "Allow authenticated full access to sales" ON sales
    FOR ALL USING (auth.role() = 'authenticated');

-- Inventory policies
CREATE POLICY "Allow authenticated full access to inventory" ON inventory
    FOR ALL USING (auth.role() = 'authenticated');

-- Order status history policies
CREATE POLICY "Allow authenticated read access to order_status_history" ON order_status_history
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated insert access to order_status_history" ON order_status_history
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- ========================================
-- STEP 9: INSERT SAMPLE DATA
-- ========================================

-- Insert sample categories
INSERT INTO categories (name, name_en, description, description_en, color_code, display_order) VALUES
('مشروبات ساخنة', 'Hot Drinks', 'قهوة وشاي ومشروبات ساخنة متنوعة', 'Coffee, tea and various hot beverages', '#D97706', 1),
('مشروبات باردة', 'Cold Drinks', 'عصائر ومشروبات باردة منعشة', 'Fresh juices and cold beverages', '#0EA5E9', 2),
('حلويات', 'Desserts', 'كيك وحلويات شرقية وغربية', 'Cakes and oriental & western desserts', '#EC4899', 3),
('معجنات', 'Pastries', 'معجنات طازجة ومخبوزات', 'Fresh pastries and baked goods', '#F59E0B', 4),
('وجبات خفيفة', 'Light Meals', 'ساندويتشات ووجبات خفيفة', 'Sandwiches and light meals', '#10B981', 5);

-- Insert sample tables
INSERT INTO tables (table_number, table_name, capacity, location) VALUES
(1, 'طاولة 1', 4, 'داخلي'),
(2, 'طاولة 2', 2, 'داخلي'),
(3, 'طاولة 3', 6, 'داخلي'),
(4, 'طاولة 4', 4, 'خارجي'),
(5, 'طاولة 5', 8, 'VIP'),
(6, 'طاولة 6', 4, 'خارجي'),
(7, 'طاولة 7', 2, 'داخلي'),
(8, 'طاولة 8', 4, 'داخلي');

-- Insert existing users data (compatible with current data)
INSERT INTO users (id, email, username, password, full_name, phone, user_type, is_active, created_at, updated_at) VALUES
('c8e1f676-9e10-488f-914f-c238daa9b75c', '<EMAIL>', 'aminovski10', 'Mam@loka10', 'مدير المقهى', '+966501234567', 'admin', true, '2025-06-13 20:57:27.989059+00', '2025-06-13 21:04:05.940761+00'),
('82523602-ad24-4170-9ff1-5beb008f7f35', '<EMAIL>', 'amine.harakat', 'Mam@loka10', 'amine harakat', '+966501234568', 'waiter', true, '2025-06-16 17:17:14.496638+00', '2025-06-16 17:18:02.328186+00'),
('b02c9cda-9c77-47ec-9d70-8186a3944997', '<EMAIL>', 'beta2', 'Mam@loka10', 'beta2', '+966501234569', 'barista', true, '2025-06-16 20:44:29.516+00', '2025-06-16 20:44:29.88279+00'),
('7eb51a33-a1ab-4264-baf8-055641dbab5d', '<EMAIL>', 'enima10', 'Mam@loka10', 'enima takarahh', '+966501234570', 'waiter', true, '2025-06-17 18:18:02.82+00', '2025-06-17 18:18:03.23324+00')
ON CONFLICT (id) DO UPDATE SET
  email = EXCLUDED.email,
  username = EXCLUDED.username,
  password = EXCLUDED.password,
  full_name = EXCLUDED.full_name,
  phone = EXCLUDED.phone,
  user_type = EXCLUDED.user_type,
  is_active = EXCLUDED.is_active,
  updated_at = NOW();

-- Insert sample waiters
INSERT INTO waiters (name, employee_id, user_id, shift_start, shift_end, hourly_rate, commission_rate) VALUES
('أحمد محمد', 'EMP001', (SELECT id FROM users WHERE username = 'admin'), '08:00', '16:00', 25.00, 5.0),
('فاطمة علي', 'EMP002', NULL, '16:00', '00:00', 25.00, 5.0),
('محمد سالم', 'EMP003', NULL, '00:00', '08:00', 30.00, 7.5),
('نورا أحمد', 'EMP004', NULL, '08:00', '16:00', 25.00, 5.0);
-- ========================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE waiters ENABLE ROW LEVEL SECURITY;
ALTER TABLE tables ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_status_history ENABLE ROW LEVEL SECURITY;

-- ========================================
-- STEP 8: CREATE RLS POLICIES
-- ========================================

-- Categories policies (public read access)
CREATE POLICY "Allow public read access to active categories" ON categories
    FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users full access to categories" ON categories
    FOR ALL USING (auth.role() = 'authenticated');

-- Products policies (public read access for available products)
CREATE POLICY "Allow public read access to available products" ON products
    FOR SELECT USING (is_available = true);

CREATE POLICY "Allow authenticated users full access to products" ON products
    FOR ALL USING (auth.role() = 'authenticated');

-- Users policies (restricted access)
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid()::text = id::text);

-- Waiters policies
CREATE POLICY "Allow authenticated read access to active waiters" ON waiters
    FOR SELECT USING (auth.role() = 'authenticated' AND is_active = true);

CREATE POLICY "Allow authenticated insert/update access to waiters" ON waiters
    FOR ALL USING (auth.role() = 'authenticated');

-- Tables policies
CREATE POLICY "Allow public read access to active tables" ON tables
    FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated full access to tables" ON tables
    FOR ALL USING (auth.role() = 'authenticated');

-- Orders policies
CREATE POLICY "Allow public insert access to orders" ON orders
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public read access to orders" ON orders
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated full access to orders" ON orders
    FOR ALL USING (auth.role() = 'authenticated');

-- Order items policies
CREATE POLICY "Allow public insert access to order_items" ON order_items
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public read access to order_items" ON order_items
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated full access to order_items" ON order_items
    FOR ALL USING (auth.role() = 'authenticated');

-- Payments policies
CREATE POLICY "Allow authenticated full access to payments" ON payments
    FOR ALL USING (auth.role() = 'authenticated');

-- Sales policies
CREATE POLICY "Allow authenticated full access to sales" ON sales
    FOR ALL USING (auth.role() = 'authenticated');

-- Inventory policies
CREATE POLICY "Allow authenticated full access to inventory" ON inventory
    FOR ALL USING (auth.role() = 'authenticated');

-- Order status history policies
CREATE POLICY "Allow authenticated read access to order_status_history" ON order_status_history
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated insert access to order_status_history" ON order_status_history
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');
-- ========================================
-- STEP 7: ENABLE ROW LEVEL SECURITY (RLS)
-- ========================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE waiters ENABLE ROW LEVEL SECURITY;
ALTER TABLE tables ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_status_history ENABLE ROW LEVEL SECURITY;

-- ========================================
-- STEP 8: CREATE RLS POLICIES
-- ========================================

-- Categories policies (public read access)
CREATE POLICY "Allow public read access to active categories" ON categories
    FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users full access to categories" ON categories
    FOR ALL USING (auth.role() = 'authenticated');

-- Products policies (public read access for available products)
CREATE POLICY "Allow public read access to available products" ON products
    FOR SELECT USING (is_available = true);

CREATE POLICY "Allow authenticated users full access to products" ON products
    FOR ALL USING (auth.role() = 'authenticated');

-- Users policies (restricted access)
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid()::text = id::text);

-- Waiters policies
CREATE POLICY "Allow authenticated read access to active waiters" ON waiters
    FOR SELECT USING (auth.role() = 'authenticated' AND is_active = true);

CREATE POLICY "Allow authenticated insert/update access to waiters" ON waiters
    FOR ALL USING (auth.role() = 'authenticated');

-- Tables policies
CREATE POLICY "Allow public read access to active tables" ON tables
    FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated full access to tables" ON tables
    FOR ALL USING (auth.role() = 'authenticated');

-- Orders policies
CREATE POLICY "Allow public insert access to orders" ON orders
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public read access to orders" ON orders
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated full access to orders" ON orders
    FOR ALL USING (auth.role() = 'authenticated');

-- Order items policies
CREATE POLICY "Allow public insert access to order_items" ON order_items
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public read access to order_items" ON order_items
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated full access to order_items" ON order_items
    FOR ALL USING (auth.role() = 'authenticated');

-- Payments policies
CREATE POLICY "Allow authenticated full access to payments" ON payments
    FOR ALL USING (auth.role() = 'authenticated');

-- Sales policies
CREATE POLICY "Allow authenticated full access to sales" ON sales
    FOR ALL USING (auth.role() = 'authenticated');

-- Inventory policies
CREATE POLICY "Allow authenticated full access to inventory" ON inventory
    FOR ALL USING (auth.role() = 'authenticated');

-- Order status history policies
CREATE POLICY "Allow authenticated read access to order_status_history" ON order_status_history
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated insert access to order_status_history" ON order_status_history
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- ========================================
-- STEP 9: INSERT SAMPLE DATA
-- ========================================

-- Insert sample categories
INSERT INTO categories (name, name_en, description, description_en, color_code, display_order) VALUES
('مشروبات ساخنة', 'Hot Drinks', 'قهوة وشاي ومشروبات ساخنة متنوعة', 'Coffee, tea and various hot beverages', '#D97706', 1),
('مشروبات باردة', 'Cold Drinks', 'عصائر ومشروبات باردة منعشة', 'Fresh juices and cold beverages', '#0EA5E9', 2),
('حلويات', 'Desserts', 'كيك وحلويات شرقية وغربية', 'Cakes and oriental & western desserts', '#EC4899', 3),
('وجبات خفيفة', 'Light Meals', 'ساندويتشات ووجبات خفيفة', 'Sandwiches and light meals', '#10B981', 4),
('إفطار', 'Breakfast', 'وجبات إفطار متنوعة', 'Various breakfast meals', '#F59E0B', 5);

-- Insert sample tables
INSERT INTO tables (table_number, table_name, capacity, location) VALUES
(1, 'طاولة 1', 4, 'داخلي'),
(2, 'طاولة 2', 2, 'داخلي'),
(3, 'طاولة 3', 6, 'داخلي'),
(4, 'طاولة 4', 4, 'خارجي'),
(5, 'طاولة 5', 8, 'VIP'),
(6, 'طاولة 6', 4, 'خارجي'),
(7, 'طاولة 7', 2, 'داخلي'),
(8, 'طاولة 8', 4, 'داخلي');

-- Insert sample admin user (password should be hashed in real application)
INSERT INTO users (email, username, password_hash, full_name, phone, role) VALUES
('<EMAIL>', 'admin', '$2b$10$rQZ8kqXvZ8kqXvZ8kqXvZe', 'مدير النظام', '+966501234567', 'admin'),
('<EMAIL>', 'manager', '$2b$10$rQZ8kqXvZ8kqXvZ8kqXvZe', 'مدير المقهى', '+966501234568', 'manager'),
('<EMAIL>', 'barista1', '$2b$10$rQZ8kqXvZ8kqXvZ8kqXvZe', 'باريستا أول', '+966501234569', 'barista');

-- Insert sample waiters
INSERT INTO waiters (name, employee_id, user_id, shift_start, shift_end, hourly_rate, commission_rate) VALUES
('أحمد محمد', 'EMP001', (SELECT id FROM users WHERE username = 'admin'), '08:00', '16:00', 25.00, 5.0),
('فاطمة علي', 'EMP002', NULL, '16:00', '00:00', 25.00, 5.0),
('محمد سالم', 'EMP003', NULL, '00:00', '08:00', 30.00, 7.5),
('نورا أحمد', 'EMP004', NULL, '08:00', '16:00', 25.00, 5.0);
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_waiters_updated_at BEFORE UPDATE ON waiters FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default categories
INSERT INTO categories (name, name_en, description, description_en, display_order) VALUES
('مشروبات ساخنة', 'Hot Drinks', 'قهوة وشاي ومشروبات ساخنة متنوعة', 'Coffee, tea and various hot beverages', 1),
('مشروبات باردة', 'Cold Drinks', 'عصائر ومشروبات باردة منعشة', 'Fresh juices and refreshing cold drinks', 2),
('حلويات', 'Desserts', 'حلويات شرقية وغربية متنوعة', 'Various Eastern and Western desserts', 3),
('معجنات', 'Pastries', 'معجنات طازجة ومخبوزات', 'Fresh pastries and baked goods', 4),
('وجبات خفيفة', 'Snacks', 'وجبات خفيفة ومقبلات', 'Light meals and appetizers', 5)
ON CONFLICT DO NOTHING;

-- Insert default admin user
INSERT INTO users (username, email, password, full_name, user_type) VALUES
('admin', '<EMAIL>', 'admin123', 'مدير المقهى', 'admin'),
('barista1', '<EMAIL>', '123456', 'أحمد محمد', 'barista'),
('waiter1', '<EMAIL>', '123456', 'محمد علي', 'waiter')
ON CONFLICT DO NOTHING;

-- Enable Row Level Security (RLS)
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE waiters ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (you can modify these based on your security needs)
-- Categories policies
CREATE POLICY "Allow read access to categories" ON categories FOR SELECT USING (true);
CREATE POLICY "Allow insert access to categories" ON categories FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow update access to categories" ON categories FOR UPDATE USING (true);

-- Products policies
CREATE POLICY "Allow read access to products" ON products FOR SELECT USING (true);
CREATE POLICY "Allow insert access to products" ON products FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow update access to products" ON products FOR UPDATE USING (true);
CREATE POLICY "Allow delete access to products" ON products FOR DELETE USING (true);

-- Users policies
CREATE POLICY "Allow read access to users" ON users FOR SELECT USING (true);
CREATE POLICY "Allow insert access to users" ON users FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow update access to users" ON users FOR UPDATE USING (true);

-- Waiters policies
CREATE POLICY "Allow read access to waiters" ON waiters FOR SELECT USING (true);
CREATE POLICY "Allow insert access to waiters" ON waiters FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow update access to waiters" ON waiters FOR UPDATE USING (true);

-- Orders policies
CREATE POLICY "Allow read access to orders" ON orders FOR SELECT USING (true);
CREATE POLICY "Allow insert access to orders" ON orders FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow update access to orders" ON orders FOR UPDATE USING (true);

-- Order items policies
CREATE POLICY "Allow read access to order_items" ON order_items FOR SELECT USING (true);
CREATE POLICY "Allow insert access to order_items" ON order_items FOR INSERT WITH CHECK (true);

-- Sales policies
CREATE POLICY "Allow read access to sales" ON sales FOR SELECT USING (true);
CREATE POLICY "Allow insert access to sales" ON sales FOR INSERT WITH CHECK (true);
