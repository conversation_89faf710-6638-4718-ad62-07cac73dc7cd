-- فحص كلمات المرور الفعلية في قاعدة البيانات
-- تشغيل هذا في SQL Editor للتحقق من كلمات المرور

-- ========================================
-- فحص جميع المستخدمين وكلمات المرور
-- ========================================

-- أولاً، دعنا نتحقق من بنية الجدول
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'users' AND table_schema = 'public'
ORDER BY ordinal_position;

-- فحص جميع المستخدمين
SELECT
    'جميع المستخدمين' as info,
    username,
    email,
    COALESCE(password, password_hash, 'لا توجد كلمة مرور') as password_field,
    full_name,
    COALESCE(user_type, role, 'غير محدد') as user_type_field,
    is_active
FROM users
ORDER BY created_at DESC;

-- ========================================
-- فحص المستخدم المحدد
-- ========================================

-- فحص المستخدم aminovski10
SELECT
    'المستخدم الأصلي' as info,
    username,
    email,
    COALESCE(password, password_hash, 'لا توجد كلمة مرور') as password_field,
    full_name,
    COALESCE(user_type, role, 'غير محدد') as user_type_field
FROM users
WHERE username = 'aminovski10';

-- فحص بالبريد الإلكتروني
SELECT
    'البحث بالإيميل' as info,
    username,
    email,
    COALESCE(password, password_hash, 'لا توجد كلمة مرور') as password_field,
    full_name,
    COALESCE(user_type, role, 'غير محدد') as user_type_field
FROM users
WHERE email = '<EMAIL>';

-- ========================================
-- تحديث كلمات المرور لتكون واضحة
-- ========================================

-- تحديث كلمة مرور المستخدم الأصلي (استخدام العمود الصحيح)
UPDATE users
SET password_hash = 'Mam@loka10'
WHERE username = 'aminovski10';

-- إضافة عمود password إذا لم يكن موجوداً
ALTER TABLE users ADD COLUMN IF NOT EXISTS password VARCHAR(255);

-- نسخ password_hash إلى password للتوافق
UPDATE users
SET password = password_hash
WHERE password IS NULL;

-- تحديث كلمة مرور المستخدم الأصلي
UPDATE users
SET
    password = 'Mam@loka10',
    password_hash = 'Mam@loka10'
WHERE username = 'aminovski10';

-- تحديث جميع المستخدمين الآخرين لنفس كلمة المرور
UPDATE users
SET
    password = 'Mam@loka10',
    password_hash = 'Mam@loka10'
WHERE username IN ('amine.harakat', 'beta2', 'enima10');

-- ========================================
-- التحقق من التحديث
-- ========================================

SELECT
    'بعد التحديث' as info,
    username,
    email,
    COALESCE(password, password_hash, 'لا توجد كلمة مرور') as password_field,
    password_hash,
    password,
    full_name,
    COALESCE(user_type, role, 'غير محدد') as user_type_field
FROM users
ORDER BY username;

-- ========================================
-- اختبار تسجيل الدخول
-- ========================================

-- اختبار المستخدم الأصلي
SELECT
    CASE
        WHEN COALESCE(password, password_hash) = 'Mam@loka10' THEN '✅ كلمة المرور صحيحة'
        ELSE '❌ كلمة المرور خاطئة: ' || COALESCE(password, password_hash, 'فارغة')
    END as test_result,
    username,
    COALESCE(password, password_hash, 'فارغة') as current_password
FROM users
WHERE username = 'aminovski10';

-- اختبار جميع المستخدمين
SELECT
    username,
    COALESCE(password, password_hash, 'فارغة') as current_password,
    CASE
        WHEN COALESCE(password, password_hash) = 'Mam@loka10' THEN '✅ صحيح'
        ELSE '❌ خطأ'
    END as password_check
FROM users
ORDER BY username;
