-- فحص كلمات المرور الفعلية في قاعدة البيانات
-- تشغيل هذا في SQL Editor للتحقق من كلمات المرور

-- ========================================
-- فحص جميع المستخدمين وكلمات المرور
-- ========================================

SELECT 
    'جميع المستخدمين' as info,
    username,
    email,
    password,
    full_name,
    user_type,
    is_active
FROM users 
ORDER BY created_at DESC;

-- ========================================
-- فحص المستخدم المحدد
-- ========================================

-- فحص المستخدم aminovski10
SELECT 
    'المستخدم الأصلي' as info,
    username,
    email,
    password,
    full_name,
    user_type
FROM users 
WHERE username = 'aminovski10';

-- فحص بالبريد الإلكتروني
SELECT 
    'البحث بالإيميل' as info,
    username,
    email,
    password,
    full_name,
    user_type
FROM users 
WHERE email = '<EMAIL>';

-- ========================================
-- تحديث كلمات المرور لتكون واضحة
-- ========================================

-- تحديث كلمة مرور المستخدم الأصلي
UPDATE users 
SET password = 'Mam@loka10'
WHERE username = 'aminovski10';

-- تحديث كلمة مرور مستخدم admin
UPDATE users 
SET password = 'admin123'
WHERE username = 'admin';

-- تحديث كلمة مرور مستخدم waiter
UPDATE users 
SET password = 'waiter123'
WHERE username = 'waiter';

-- تحديث جميع المستخدمين الآخرين لنفس كلمة المرور
UPDATE users 
SET password = 'Mam@loka10'
WHERE username IN ('amine.harakat', 'beta2', 'enima10');

-- ========================================
-- التحقق من التحديث
-- ========================================

SELECT 
    'بعد التحديث' as info,
    username,
    email,
    password,
    full_name,
    user_type
FROM users 
ORDER BY username;

-- ========================================
-- اختبار تسجيل الدخول
-- ========================================

-- اختبار المستخدم الأصلي
SELECT 
    CASE 
        WHEN password = 'Mam@loka10' THEN '✅ كلمة المرور صحيحة'
        ELSE '❌ كلمة المرور خاطئة: ' || password
    END as test_result,
    username,
    password
FROM users 
WHERE username = 'aminovski10';

-- اختبار مستخدم admin
SELECT 
    CASE 
        WHEN password = 'admin123' THEN '✅ كلمة المرور صحيحة'
        ELSE '❌ كلمة المرور خاطئة: ' || password
    END as test_result,
    username,
    password
FROM users 
WHERE username = 'admin';
