-- Avie Cafe Database Update Script
-- تشغيل هذا الملف لتحديث قاعدة البيانات الحالية بدون حذف البيانات الموجودة

-- ========================================
-- STEP 1: ADD MISSING COLUMNS TO EXISTING TABLES
-- ========================================

-- تحديث جدول users لإضافة الأعمدة المفقودة
ALTER TABLE users ADD COLUMN IF NOT EXISTS phone VARCHAR(20);
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE;

-- تحديث جدول categories لإضافة الأعمدة المفقودة
ALTER TABLE categories ADD COLUMN IF NOT EXISTS color_code VARCHAR(7) DEFAULT '#3B82F6';
ALTER TABLE categories ADD COLUMN IF NOT EXISTS image_url TEXT;

-- تحديث جدول products لإضافة الأعمدة المفقودة
ALTER TABLE products ADD COLUMN IF NOT EXISTS cost_price DECIMAL(10,2);
ALTER TABLE products ADD COLUMN IF NOT EXISTS sku VARCHAR(50) UNIQUE;
ALTER TABLE products ADD COLUMN IF NOT EXISTS barcode VARCHAR(100);
ALTER TABLE products ADD COLUMN IF NOT EXISTS preparation_time INTEGER DEFAULT 5;
ALTER TABLE products ADD COLUMN IF NOT EXISTS calories INTEGER;
ALTER TABLE products ADD COLUMN IF NOT EXISTS allergens TEXT[];
ALTER TABLE products ADD COLUMN IF NOT EXISTS tags TEXT[];

-- ========================================
-- STEP 2: CREATE MISSING TABLES
-- ========================================

-- Create tables table (for restaurant table management)
CREATE TABLE IF NOT EXISTS tables (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  table_number INTEGER UNIQUE NOT NULL,
  table_name VARCHAR(50),
  capacity INTEGER NOT NULL DEFAULT 4,
  location VARCHAR(100), -- e.g., 'Indoor', 'Outdoor', 'VIP'
  status VARCHAR(20) DEFAULT 'available' 
    CHECK (status IN ('available', 'occupied', 'reserved', 'maintenance')),
  qr_code TEXT, -- QR code for digital menu
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT tables_number_positive CHECK (table_number > 0),
  CONSTRAINT tables_capacity_positive CHECK (capacity > 0)
);

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  payment_method VARCHAR(50) NOT NULL
    CHECK (payment_method IN ('cash', 'card', 'mobile', 'bank_transfer', 'voucher')),
  payment_status VARCHAR(20) DEFAULT 'completed'
    CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded')),
  transaction_id VARCHAR(100),
  reference_number VARCHAR(100),
  processed_by_id UUID REFERENCES users(id) ON DELETE SET NULL,
  processed_by_name VARCHAR(200),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT payments_amount_positive CHECK (amount > 0)
);

-- Create inventory table (for stock management)
CREATE TABLE IF NOT EXISTS inventory (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  current_stock INTEGER NOT NULL DEFAULT 0,
  minimum_stock INTEGER DEFAULT 0,
  maximum_stock INTEGER,
  unit_of_measure VARCHAR(20) DEFAULT 'piece',
  last_restocked_at TIMESTAMP WITH TIME ZONE,
  last_restocked_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT inventory_current_stock_positive CHECK (current_stock >= 0),
  CONSTRAINT inventory_minimum_stock_positive CHECK (minimum_stock >= 0),
  CONSTRAINT inventory_maximum_stock_valid CHECK (maximum_stock IS NULL OR maximum_stock >= minimum_stock),
  
  -- Unique constraint
  UNIQUE(product_id)
);

-- Create order_status_history table (for tracking order status changes)
CREATE TABLE IF NOT EXISTS order_status_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  old_status VARCHAR(20),
  new_status VARCHAR(20) NOT NULL,
  changed_by_id UUID REFERENCES users(id) ON DELETE SET NULL,
  changed_by_name VARCHAR(200),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================
-- STEP 3: UPDATE EXISTING ORDERS TABLE
-- ========================================

-- إضافة الأعمدة المفقودة لجدول orders
ALTER TABLE orders ADD COLUMN IF NOT EXISTS order_number VARCHAR(20) UNIQUE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS table_id UUID REFERENCES tables(id) ON DELETE SET NULL;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS customer_phone VARCHAR(20);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS customer_email VARCHAR(255);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS special_requests TEXT;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(10,2) DEFAULT 0;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS tax_rate DECIMAL(5,2) DEFAULT 15;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS service_charge DECIMAL(10,2) DEFAULT 0;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_status VARCHAR(20) DEFAULT 'unpaid';
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_method VARCHAR(50);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS order_type VARCHAR(20) DEFAULT 'dine_in';
ALTER TABLE orders ADD COLUMN IF NOT EXISTS estimated_prep_time INTEGER;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS actual_prep_time INTEGER;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS order_date DATE DEFAULT CURRENT_DATE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;

-- إضافة constraints للأعمدة الجديدة
DO $$ 
BEGIN
    -- Add check constraints if they don't exist
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'orders_payment_status_check') THEN
        ALTER TABLE orders ADD CONSTRAINT orders_payment_status_check 
        CHECK (payment_status IN ('unpaid', 'partial', 'paid', 'refunded'));
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'orders_order_type_check') THEN
        ALTER TABLE orders ADD CONSTRAINT orders_order_type_check 
        CHECK (order_type IN ('dine_in', 'takeaway', 'delivery'));
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'orders_discount_valid_check') THEN
        ALTER TABLE orders ADD CONSTRAINT orders_discount_valid_check 
        CHECK (discount_amount >= 0);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'orders_tax_rate_valid_check') THEN
        ALTER TABLE orders ADD CONSTRAINT orders_tax_rate_valid_check 
        CHECK (tax_rate >= 0 AND tax_rate <= 100);
    END IF;
END $$;

-- ========================================
-- STEP 4: UPDATE EXISTING ORDER_ITEMS TABLE
-- ========================================

-- إضافة الأعمدة المفقودة لجدول order_items
ALTER TABLE order_items ADD COLUMN IF NOT EXISTS product_name VARCHAR(200);
ALTER TABLE order_items ADD COLUMN IF NOT EXISTS special_instructions TEXT;
ALTER TABLE order_items ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'pending';

-- إضافة constraint للحالة
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'order_items_status_check') THEN
        ALTER TABLE order_items ADD CONSTRAINT order_items_status_check 
        CHECK (status IN ('pending', 'confirmed', 'preparing', 'ready', 'served', 'cancelled'));
    END IF;
END $$;

-- تحديث product_name للعناصر الموجودة
UPDATE order_items 
SET product_name = p.name 
FROM products p 
WHERE order_items.product_id = p.id 
AND order_items.product_name IS NULL;

-- ========================================
-- STEP 5: UPDATE EXISTING WAITERS TABLE
-- ========================================

-- إضافة الأعمدة المفقودة لجدول waiters
ALTER TABLE waiters ADD COLUMN IF NOT EXISTS employee_id VARCHAR(20) UNIQUE;
ALTER TABLE waiters ADD COLUMN IF NOT EXISTS shift_start TIME;
ALTER TABLE waiters ADD COLUMN IF NOT EXISTS shift_end TIME;
ALTER TABLE waiters ADD COLUMN IF NOT EXISTS hourly_rate DECIMAL(8,2);
ALTER TABLE waiters ADD COLUMN IF NOT EXISTS commission_rate DECIMAL(5,2) DEFAULT 0;
ALTER TABLE waiters ADD COLUMN IF NOT EXISTS current_status VARCHAR(20) DEFAULT 'offline';
ALTER TABLE waiters ADD COLUMN IF NOT EXISTS total_orders_today INTEGER DEFAULT 0;
ALTER TABLE waiters ADD COLUMN IF NOT EXISTS total_sales_today DECIMAL(10,2) DEFAULT 0;

-- إضافة constraints
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'waiters_current_status_check') THEN
        ALTER TABLE waiters ADD CONSTRAINT waiters_current_status_check 
        CHECK (current_status IN ('online', 'offline', 'busy', 'break'));
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'waiters_commission_rate_valid_check') THEN
        ALTER TABLE waiters ADD CONSTRAINT waiters_commission_rate_valid_check 
        CHECK (commission_rate >= 0 AND commission_rate <= 100);
    END IF;
END $$;

-- ========================================
-- STEP 6: UPDATE EXISTING SALES TABLE
-- ========================================

-- إضافة الأعمدة المفقودة لجدول sales
ALTER TABLE sales ADD COLUMN IF NOT EXISTS cashier_id UUID REFERENCES users(id) ON DELETE SET NULL;
ALTER TABLE sales ADD COLUMN IF NOT EXISTS cashier_name VARCHAR(200);
ALTER TABLE sales ADD COLUMN IF NOT EXISTS gross_amount DECIMAL(10,2);
ALTER TABLE sales ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(10,2) DEFAULT 0;
ALTER TABLE sales ADD COLUMN IF NOT EXISTS tax_amount DECIMAL(10,2) DEFAULT 0;
ALTER TABLE sales ADD COLUMN IF NOT EXISTS net_amount DECIMAL(10,2);
ALTER TABLE sales ADD COLUMN IF NOT EXISTS commission_amount DECIMAL(10,2) DEFAULT 0;
ALTER TABLE sales ADD COLUMN IF NOT EXISTS sale_time TIME DEFAULT CURRENT_TIME;

-- تحديث البيانات الموجودة
UPDATE sales 
SET 
    gross_amount = COALESCE(gross_amount, amount),
    net_amount = COALESCE(net_amount, amount)
WHERE gross_amount IS NULL OR net_amount IS NULL;

-- ========================================
-- STEP 7: INSERT SAMPLE DATA FOR NEW TABLES
-- ========================================

-- Insert sample tables
INSERT INTO tables (table_number, table_name, capacity, location) VALUES
(1, 'طاولة 1', 4, 'داخلي'),
(2, 'طاولة 2', 2, 'داخلي'),
(3, 'طاولة 3', 6, 'داخلي'),
(4, 'طاولة 4', 4, 'خارجي'),
(5, 'طاولة 5', 8, 'VIP'),
(6, 'طاولة 6', 4, 'خارجي'),
(7, 'طاولة 7', 2, 'داخلي'),
(8, 'طاولة 8', 4, 'داخلي')
ON CONFLICT (table_number) DO NOTHING;

-- Update categories with colors
UPDATE categories SET 
    color_code = CASE 
        WHEN name = 'مشروبات ساخنة' THEN '#D97706'
        WHEN name = 'مشروبات باردة' THEN '#0EA5E9'
        WHEN name = 'حلويات' THEN '#EC4899'
        WHEN name = 'وجبات خفيفة' THEN '#10B981'
        ELSE '#3B82F6'
    END
WHERE color_code IS NULL OR color_code = '#3B82F6';

-- ========================================
-- STEP 8: CREATE FUNCTIONS AND TRIGGERS
-- ========================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to generate order number
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL THEN
        NEW.order_number := 'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD(NEXTVAL('order_number_seq')::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for order numbers
CREATE SEQUENCE IF NOT EXISTS order_number_seq START 1;

-- Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_categories_updated_at ON categories;
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_products_updated_at ON products;
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_waiters_updated_at ON waiters;
CREATE TRIGGER update_waiters_updated_at BEFORE UPDATE ON waiters
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_tables_updated_at ON tables;
CREATE TRIGGER update_tables_updated_at BEFORE UPDATE ON tables
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_orders_updated_at ON orders;
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_order_items_updated_at ON order_items;
CREATE TRIGGER update_order_items_updated_at BEFORE UPDATE ON order_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_inventory_updated_at ON inventory;
CREATE TRIGGER update_inventory_updated_at BEFORE UPDATE ON inventory
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger for order number generation
DROP TRIGGER IF EXISTS generate_order_number_trigger ON orders;
CREATE TRIGGER generate_order_number_trigger BEFORE INSERT ON orders
    FOR EACH ROW EXECUTE FUNCTION generate_order_number();

-- ========================================
-- FINAL MESSAGE
-- ========================================

-- تم تحديث قاعدة البيانات بنجاح!
-- جميع الجداول والأعمدة الجديدة تم إضافتها
-- البيانات الموجودة محفوظة ولم يتم حذفها
