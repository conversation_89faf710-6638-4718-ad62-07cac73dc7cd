// اختبار الاتصال بـ Supabase
// شغل هذا الكود في Console في المتصفح (F12)

// نسخ إعدادات Supabase
const supabaseUrl = "https://tjivtrkbjsesulrthxpr.supabase.co";
const supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw";

// اختبار الاتصال الأساسي
async function testSupabaseConnection() {
    console.log("🔄 اختبار الاتصال بـ Supabase...");
    
    try {
        // اختبار HTTP request مباشر
        const response = await fetch(`${supabaseUrl}/rest/v1/users?select=*&limit=1`, {
            headers: {
                'apikey': supabaseKey,
                'Authorization': `Bearer ${supabaseKey}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log("✅ الاتصال ناجح!");
            console.log("📊 البيانات المستلمة:", data);
            return true;
        } else {
            console.error("❌ فشل الاتصال:", response.status, response.statusText);
            const errorText = await response.text();
            console.error("تفاصيل الخطأ:", errorText);
            return false;
        }
    } catch (error) {
        console.error("❌ خطأ في الشبكة:", error);
        return false;
    }
}

// اختبار تسجيل الدخول
async function testLogin(username, password) {
    console.log(`🔐 اختبار تسجيل الدخول للمستخدم: ${username}`);
    
    try {
        // البحث بـ username
        const response = await fetch(`${supabaseUrl}/rest/v1/users?username=eq.${username}&select=*`, {
            headers: {
                'apikey': supabaseKey,
                'Authorization': `Bearer ${supabaseKey}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const users = await response.json();
            console.log("📋 نتائج البحث:", users);
            
            if (users.length === 0) {
                console.log("❌ المستخدم غير موجود");
                return false;
            }
            
            const user = users[0];
            if (user.password === password) {
                console.log("✅ تسجيل الدخول ناجح!");
                console.log("👤 بيانات المستخدم:", {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    full_name: user.full_name,
                    user_type: user.user_type
                });
                return true;
            } else {
                console.log("❌ كلمة المرور غير صحيحة");
                console.log("🔍 كلمة المرور المحفوظة:", user.password);
                console.log("🔍 كلمة المرور المدخلة:", password);
                return false;
            }
        } else {
            console.error("❌ فشل البحث:", response.status, response.statusText);
            return false;
        }
    } catch (error) {
        console.error("❌ خطأ في اختبار تسجيل الدخول:", error);
        return false;
    }
}

// اختبار جميع المستخدمين
async function testAllUsers() {
    console.log("📋 جلب جميع المستخدمين...");
    
    try {
        const response = await fetch(`${supabaseUrl}/rest/v1/users?select=*`, {
            headers: {
                'apikey': supabaseKey,
                'Authorization': `Bearer ${supabaseKey}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const users = await response.json();
            console.log("👥 جميع المستخدمين:", users);
            
            console.log("\n📊 ملخص المستخدمين:");
            users.forEach((user, index) => {
                console.log(`${index + 1}. ${user.username} (${user.email}) - ${user.user_type}`);
            });
            
            return users;
        } else {
            console.error("❌ فشل جلب المستخدمين:", response.status, response.statusText);
            return [];
        }
    } catch (error) {
        console.error("❌ خطأ في جلب المستخدمين:", error);
        return [];
    }
}

// تشغيل جميع الاختبارات
async function runAllTests() {
    console.log("🚀 بدء اختبارات Supabase...\n");
    
    // اختبار الاتصال
    const connectionOk = await testSupabaseConnection();
    if (!connectionOk) {
        console.log("❌ فشل الاتصال. توقف الاختبار.");
        return;
    }
    
    console.log("\n" + "=".repeat(50) + "\n");
    
    // جلب جميع المستخدمين
    const users = await testAllUsers();
    
    console.log("\n" + "=".repeat(50) + "\n");
    
    // اختبار تسجيل الدخول للمستخدمين المختلفين
    const testCredentials = [
        { username: "aminovski10", password: "Mam@loka10" },
        { username: "admin", password: "admin123" },
        { username: "waiter", password: "waiter123" },
        { username: "amine.harakat", password: "Mam@loka10" }
    ];
    
    for (const cred of testCredentials) {
        await testLogin(cred.username, cred.password);
        console.log("\n" + "-".repeat(30) + "\n");
    }
    
    console.log("✅ انتهت جميع الاختبارات!");
}

// تشغيل الاختبارات تلقائياً
console.log("🎯 لتشغيل الاختبارات، استخدم:");
console.log("runAllTests()");
console.log("\n🔍 لاختبار مستخدم محدد:");
console.log('testLogin("aminovski10", "Mam@loka10")');
console.log("\n📋 لجلب جميع المستخدمين:");
console.log("testAllUsers()");

// تشغيل تلقائي
runAllTests();
