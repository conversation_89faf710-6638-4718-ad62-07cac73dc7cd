-- حل سريع لمشكلة تسجيل الدخول
-- تشغيل هذا الملف في SQL Editor في Supabase

-- ========================================
-- الخطوة 1: إيقاف RLS مؤقتاً للاختبار
-- ========================================

-- إيقاف RLS لجدول users
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- إيقاف R<PERSON> للجداول الأخرى المطلوبة
ALTER TABLE categories DISABLE ROW LEVEL SECURITY;
ALTER TABLE products DISABLE ROW LEVEL SECURITY;
ALTER TABLE orders DISABLE ROW LEVEL SECURITY;
ALTER TABLE order_items DISABLE ROW LEVEL SECURITY;
ALTER TABLE waiters DISABLE ROW LEVEL SECURITY;
ALTER TABLE sales DISABLE ROW LEVEL SECURITY;

-- ========================================
-- الخطوة 2: التأكد من وجود البيانات
-- ========================================

-- فحص المستخدمين الموجودين
SELECT 
    id,
    username,
    email,
    full_name,
    user_type,
    is_active,
    created_at
FROM users
ORDER BY created_at DESC;

-- ========================================
-- الخطوة 3: إضافة مستخدم اختبار إضافي
-- ========================================

-- إضافة مستخدم admin بسيط للاختبار
INSERT INTO users (
    id,
    username,
    email,
    password,
    full_name,
    user_type,
    is_active,
    created_at,
    updated_at
) VALUES (
    'admin-test-123',
    'admin',
    '<EMAIL>',
    'admin123',
    'مدير الاختبار',
    'admin',
    true,
    NOW(),
    NOW()
) ON CONFLICT (username) DO UPDATE SET
    password = 'admin123',
    full_name = 'مدير الاختبار',
    user_type = 'admin',
    is_active = true,
    updated_at = NOW();

-- إضافة مستخدم waiter للاختبار
INSERT INTO users (
    id,
    username,
    email,
    password,
    full_name,
    user_type,
    is_active,
    created_at,
    updated_at
) VALUES (
    'waiter-test-123',
    'waiter',
    '<EMAIL>',
    'waiter123',
    'نادل الاختبار',
    'waiter',
    true,
    NOW(),
    NOW()
) ON CONFLICT (username) DO UPDATE SET
    password = 'waiter123',
    full_name = 'نادل الاختبار',
    user_type = 'waiter',
    is_active = true,
    updated_at = NOW();

-- ========================================
-- الخطوة 4: التحقق من النتائج
-- ========================================

-- اختبار البحث بـ username
SELECT 
    'Test getByUsername' as test_type,
    id,
    username,
    email,
    password,
    full_name,
    user_type
FROM users 
WHERE username = 'admin';

-- اختبار البحث بـ email
SELECT 
    'Test getByEmail' as test_type,
    id,
    username,
    email,
    password,
    full_name,
    user_type
FROM users 
WHERE email = '<EMAIL>';

-- ========================================
-- الخطوة 5: رسالة النجاح
-- ========================================

-- إذا ظهرت النتائج أعلاه، فالمشكلة محلولة!
-- يمكنك الآن تسجيل الدخول باستخدام:
-- 
-- المستخدم الأصلي:
-- username: aminovski10
-- password: Mam@loka10
--
-- أو مستخدم الاختبار:
-- username: admin
-- password: admin123
--
-- أو:
-- username: waiter  
-- password: waiter123

-- ========================================
-- ملاحظات مهمة
-- ========================================

-- 1. تم إيقاف RLS مؤقتاً للاختبار
-- 2. في الإنتاج، يجب إعادة تفعيل RLS مع السياسات المناسبة
-- 3. كلمات المرور غير مشفرة للاختبار فقط
-- 4. تأكد من تحديث إعدادات Supabase إذا لزم الأمر

-- للتحقق من حالة RLS:
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('users', 'products', 'categories', 'orders');
