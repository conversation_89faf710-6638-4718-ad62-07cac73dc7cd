-- حل بسيط وسريع لمشكلة كلمة المرور
-- تشغيل هذا الملف في SQL Editor

-- ========================================
-- الخطوة 1: فحص بنية الجدول
-- ========================================

-- فحص الأعمدة الموجودة في جدول users
SELECT 
    'أعمدة جدول users' as info,
    column_name, 
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'users' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- ========================================
-- الخطوة 2: فحص البيانات الحالية
-- ========================================

-- فحص جميع المستخدمين (بدون تحديد أسماء أعمدة محددة)
SELECT * FROM users ORDER BY created_at DESC;

-- ========================================
-- الخطوة 3: إضافة عمود password إذا لم يكن موجوداً
-- ========================================

-- إضافة عمود password للتوافق مع الكود
ALTER TABLE users ADD COLUMN IF NOT EXISTS password VARCHAR(255);

-- ========================================
-- الخطوة 4: تحديث كلمات المرور
-- ========================================

-- تحديث المستخدم الأصلي
UPDATE users 
SET password = 'Mam@loka10'
WHERE username = 'aminovski10';

-- تحديث المستخدمين الآخرين
UPDATE users 
SET password = 'Mam@loka10'
WHERE username IN ('amine.harakat', 'beta2', 'enima10');

-- إضافة مستخدمين للاختبار
INSERT INTO users (username, email, password, full_name, user_type, is_active) 
VALUES 
    ('admin', '<EMAIL>', 'admin123', 'مدير الاختبار', 'admin', true),
    ('waiter', '<EMAIL>', 'waiter123', 'نادل الاختبار', 'waiter', true),
    ('barista', '<EMAIL>', 'barista123', 'باريستا الاختبار', 'barista', true)
ON CONFLICT (username) DO UPDATE SET
    password = EXCLUDED.password,
    user_type = EXCLUDED.user_type,
    is_active = true;

-- ========================================
-- الخطوة 5: التحقق من النتائج
-- ========================================

-- عرض جميع المستخدمين مع كلمات المرور
SELECT 
    '✅ المستخدمين المحدثين' as status,
    username,
    email,
    password,
    full_name,
    user_type,
    is_active
FROM users 
WHERE password IS NOT NULL
ORDER BY 
    CASE user_type 
        WHEN 'admin' THEN 1
        WHEN 'manager' THEN 2
        WHEN 'barista' THEN 3
        WHEN 'waiter' THEN 4
        ELSE 5
    END,
    username;

-- ========================================
-- الخطوة 6: اختبار تسجيل الدخول
-- ========================================

-- اختبار المستخدم الأصلي
SELECT 
    '🔐 اختبار aminovski10' as test,
    username,
    password,
    CASE 
        WHEN password = 'Mam@loka10' THEN '✅ يمكن تسجيل الدخول'
        ELSE '❌ كلمة المرور خاطئة'
    END as login_status
FROM users 
WHERE username = 'aminovski10';

-- اختبار مستخدم admin
SELECT 
    '🔐 اختبار admin' as test,
    username,
    password,
    CASE 
        WHEN password = 'admin123' THEN '✅ يمكن تسجيل الدخول'
        ELSE '❌ كلمة المرور خاطئة'
    END as login_status
FROM users 
WHERE username = 'admin';

-- ========================================
-- رسالة النجاح
-- ========================================

SELECT 
    '🎉 تم إصلاح المشكلة بنجاح!' as message,
    'يمكنك الآن تسجيل الدخول باستخدام البيانات التالية:' as instructions;

SELECT 
    '📋 بيانات تسجيل الدخول' as info,
    username + ' / ' + password as credentials,
    user_type as role
FROM users 
WHERE password IS NOT NULL 
AND is_active = true
ORDER BY user_type, username;
